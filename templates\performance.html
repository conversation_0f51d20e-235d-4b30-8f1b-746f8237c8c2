<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Dashboard - XAUUSD Trading Analysis</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .metric-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-5px);
        }
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .trend-up { color: #28a745; }
        .trend-down { color: #dc3545; }
        .trend-stable { color: #6c757d; }
        .weight-bar {
            height: 20px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            position: relative;
        }
        .recommendation-item {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 0 5px 5px 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>XAUUSD Analysis Pro
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>New Analysis</a>
                <a class="nav-link" href="/history"><i class="fas fa-history me-1"></i>History</a>
                <a class="nav-link active" href="/performance"><i class="fas fa-chart-bar me-1"></i>Performance</a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-chart-bar me-3"></i>Performance Dashboard
                </h1>
                <p class="text-muted">Monitor and improve your trading analysis system performance</p>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card text-center">
                    <div class="card-body">
                        <i class="fas fa-comments fa-2x text-primary mb-3"></i>
                        <div class="metric-value text-primary">{{ performance.total_feedback }}</div>
                        <h6 class="card-title">Total Feedback</h6>
                        <small class="text-muted">Analysis reviews</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card text-center">
                    <div class="card-body">
                        <i class="fas fa-star fa-2x text-warning mb-3"></i>
                        <div class="metric-value text-warning">{{ performance.average_accuracy }}</div>
                        <h6 class="card-title">Avg Accuracy</h6>
                        <small class="text-muted">Out of 5 stars</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card text-center">
                    <div class="card-body">
                        <i class="fas fa-trophy fa-2x text-success mb-3"></i>
                        <div class="metric-value text-success">{{ (performance.success_rate * 100)|round|int }}%</div>
                        <h6 class="card-title">Success Rate</h6>
                        <small class="text-muted">Profitable/accurate trades</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card text-center">
                    <div class="card-body">
                        <i class="fas fa-chart-line fa-2x 
                            {% if performance.recent_trend == 'improving' %}text-success
                            {% elif performance.recent_trend == 'declining' %}text-danger
                            {% else %}text-secondary{% endif %} mb-3"></i>
                        <div class="metric-value 
                            {% if performance.recent_trend == 'improving' %}trend-up
                            {% elif performance.recent_trend == 'declining' %}trend-down
                            {% else %}trend-stable{% endif %}">
                            {% if performance.recent_trend == 'improving' %}
                                <i class="fas fa-arrow-up"></i>
                            {% elif performance.recent_trend == 'declining' %}
                                <i class="fas fa-arrow-down"></i>
                            {% else %}
                                <i class="fas fa-minus"></i>
                            {% endif %}
                        </div>
                        <h6 class="card-title">Trend</h6>
                        <small class="text-muted">{{ performance.recent_trend|replace('_', ' ')|title }}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Strategy Performance -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-chess me-2"></i>Strategy Performance</h5>
                    </div>
                    <div class="card-body">
                        {% if performance.strategy_performance %}
                        <div class="row">
                            {% for strategy, stats in performance.strategy_performance.items() %}
                            <div class="col-md-6 mb-3">
                                <div class="border rounded p-3">
                                    <h6 class="fw-bold">{{ strategy|title }} Strategy</h6>
                                    <div class="row">
                                        <div class="col-4">
                                            <div class="text-center">
                                                <div class="fw-bold text-primary">{{ stats.count }}</div>
                                                <small class="text-muted">Analyses</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-center">
                                                <div class="fw-bold text-warning">{{ stats.average_score }}</div>
                                                <small class="text-muted">Avg Score</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-center">
                                                <div class="fw-bold text-success">{{ (stats.success_rate * 100)|round|int }}%</div>
                                                <small class="text-muted">Success</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No strategy performance data available yet.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Learning System Weights -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-brain me-2"></i>Learning System Weights</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">
                            These weights show how much the system values each analysis component based on feedback.
                        </p>
                        {% if performance.current_weights %}
                        <div class="row">
                            {% for component, weight in performance.current_weights.items() %}
                            <div class="col-md-6 mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="fw-bold">{{ component|replace('_', ' ')|title }}</span>
                                    <span class="badge bg-primary">{{ weight|round(2) }}</span>
                                </div>
                                <div class="weight-bar">
                                    <div class="position-absolute top-0 start-0 h-100 bg-white rounded" 
                                         style="width: {{ (weight / 2.0 * 100)|round }}%; opacity: 0.3;"></div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Thresholds -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-sliders-h me-2"></i>Current Thresholds</h5>
                    </div>
                    <div class="card-body">
                        {% if performance.current_thresholds %}
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="fw-bold text-info">{{ (performance.current_thresholds.confidence * 100)|round|int }}%</div>
                                    <small class="text-muted">Confidence Threshold</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="fw-bold text-info">{{ performance.current_thresholds.learning_rate }}</div>
                                    <small class="text-muted">Learning Rate</small>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>System Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="exportData()">
                                <i class="fas fa-download me-2"></i>Export Performance Data
                            </button>
                            <button class="btn btn-outline-warning" onclick="resetSystem()">
                                <i class="fas fa-redo me-2"></i>Reset Learning System
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendations -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Improvement Recommendations</h5>
                    </div>
                    <div class="card-body">
                        {% if recommendations %}
                        {% for recommendation in recommendations %}
                        <div class="recommendation-item">
                            <i class="fas fa-arrow-right me-2 text-primary"></i>{{ recommendation }}
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-success">System Performing Well!</h5>
                            <p class="text-muted">No specific recommendations at this time. Keep collecting feedback to maintain performance.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        function exportData() {
            // In a real implementation, this would export performance data
            alert('Export functionality would be implemented here');
        }
        
        function resetSystem() {
            if (confirm('Are you sure you want to reset the learning system? This will clear all learned weights and start fresh.')) {
                // In a real implementation, this would call the reset endpoint
                alert('Reset functionality would be implemented here');
            }
        }
    </script>
</body>
</html>
