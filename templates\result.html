<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XAUUSD Analysis Results</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .analysis-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
        }
        .score-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
            color: white;
        }
        .score-high { background: linear-gradient(45deg, #28a745, #20c997); }
        .score-medium { background: linear-gradient(45deg, #ffc107, #fd7e14); }
        .score-low { background: linear-gradient(45deg, #dc3545, #e83e8c); }

        .action-buy {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        .action-sell {
            background: linear-gradient(45deg, #dc3545, #e83e8c);
            color: white;
        }
        .action-wait {
            background: linear-gradient(45deg, #6c757d, #adb5bd);
            color: white;
        }

        .level-badge {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            margin: 0.2rem;
            display: inline-block;
        }

        .methodology-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>XAUUSD Analysis Pro
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>New Analysis</a>
                <a class="nav-link" href="/history"><i class="fas fa-history me-1"></i>History</a>
                <a class="nav-link" href="/performance"><i class="fas fa-chart-bar me-1"></i>Performance</a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-5 fw-bold text-center mb-3">
                    <i class="fas fa-chart-line me-3"></i>XAUUSD Analysis Results
                </h1>
                <p class="text-center text-muted">
                    Analysis completed using Smart Money Concepts and ICT methodologies
                </p>
            </div>
        </div>

        <!-- Main Recommendation -->
        {% set recommendation = analysis.recommendation %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card analysis-card">
                    <div class="card-header action-{{ recommendation.action }} text-center">
                        <h3 class="mb-0">
                            {% if recommendation.action == 'buy' %}
                                <i class="fas fa-arrow-up me-2"></i>BUY RECOMMENDATION
                            {% elif recommendation.action == 'sell' %}
                                <i class="fas fa-arrow-down me-2"></i>SELL RECOMMENDATION
                            {% else %}
                                <i class="fas fa-pause me-2"></i>WAIT / NO TRADE
                            {% endif %}
                        </h3>
                        <p class="mb-0">{{ recommendation.entry_logic }}</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <div class="score-circle
                                    {% if recommendation.confidence >= 0.7 %}score-high
                                    {% elif recommendation.confidence >= 0.5 %}score-medium
                                    {% else %}score-low{% endif %} mx-auto">
                                    {{ (recommendation.confidence * 100)|round|int }}%
                                </div>
                                <small class="text-muted">Confidence</small>
                            </div>
                            <div class="col-md-9">
                                <h5>Strategy: {{ recommendation.strategy|title }}</h5>
                                <p><strong>Timeframes:</strong>
                                    {% for tf in recommendation.timeframe_focus %}
                                        <span class="badge bg-secondary me-1">{{ tf }}</span>
                                    {% endfor %}
                                </p>

                                {% if recommendation.levels %}
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>Entry:</strong> ${{ recommendation.levels.entry }}<br>
                                        <strong>Stop Loss:</strong> ${{ recommendation.levels.stop_loss }}
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Take Profits:</strong><br>
                                        TP1: ${{ recommendation.levels.take_profit_1 }} ({{ recommendation.levels.risk_reward_1 }}R)<br>
                                        TP2: ${{ recommendation.levels.take_profit_2 }} ({{ recommendation.levels.risk_reward_2 }}R)<br>
                                        TP3: ${{ recommendation.levels.take_profit_3 }} ({{ recommendation.levels.risk_reward_3 }}R)
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis Details -->
        <div class="row">
            <!-- SMC Analysis -->
            <div class="col-lg-6 mb-4">
                <div class="card analysis-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-brain me-2"></i>Smart Money Concepts</h5>
                    </div>
                    <div class="card-body">
                        {% set smc = analysis.smc_analysis %}
                        <div class="row mb-3">
                            <div class="col-6">
                                <strong>SMC Score:</strong>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-primary" style="width: {{ (smc.smc_score * 100)|round }}%">
                                        {{ (smc.smc_score * 100)|round }}%
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <strong>Market Structure:</strong><br>
                                <span class="badge bg-info">{{ smc.market_structure.state|replace('_', ' ')|title }}</span>
                            </div>
                        </div>

                        <h6>Order Blocks:</h6>
                        <p>
                            Bullish: {{ smc.order_blocks.bullish_order_blocks.count }}
                            ({{ smc.order_blocks.bullish_order_blocks.strength }})<br>
                            Bearish: {{ smc.order_blocks.bearish_order_blocks.count }}
                            ({{ smc.order_blocks.bearish_order_blocks.strength }})
                        </p>

                        <h6>Fair Value Gaps:</h6>
                        <p>Count: {{ smc.fair_value_gaps.count }} | Strength: {{ smc.fair_value_gaps.strength }}</p>

                        <h6>Smart Money Bias:</h6>
                        <span class="badge
                            {% if 'bullish' in smc.institutional_flow.smart_money_bias %}bg-success
                            {% elif 'bearish' in smc.institutional_flow.smart_money_bias %}bg-danger
                            {% else %}bg-secondary{% endif %}">
                            {{ smc.institutional_flow.smart_money_bias|replace('_', ' ')|title }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- ICT Analysis -->
            <div class="col-lg-6 mb-4">
                <div class="card analysis-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>ICT Analysis</h5>
                    </div>
                    <div class="card-body">
                        {% set ict = analysis.ict_analysis %}
                        <div class="row mb-3">
                            <div class="col-6">
                                <strong>ICT Score:</strong>
                                <div class="progress mt-1">
                                    <div class="progress-bar bg-success" style="width: {{ (ict.ict_score * 100)|round }}%">
                                        {{ (ict.ict_score * 100)|round }}%
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <strong>Kill Zone:</strong><br>
                                <span class="badge bg-warning text-dark">{{ ict.kill_zone_analysis.active_zone or 'Outside' }}</span>
                            </div>
                        </div>

                        <h6>Premium/Discount:</h6>
                        <p>
                            Current Level:
                            <span class="badge
                                {% if ict.premium_discount.current_level == 'premium' %}bg-danger
                                {% elif ict.premium_discount.current_level == 'discount' %}bg-success
                                {% else %}bg-secondary{% endif %}">
                                {{ ict.premium_discount.current_level|title }}
                            </span><br>
                            Recommendation: {{ ict.premium_discount.recommendation|replace('_', ' ')|title }}
                        </p>

                        <h6>Market Maker Model:</h6>
                        <p>Phase: {{ ict.market_maker_model.phase|title }}</p>

                        <h6>Order Flow:</h6>
                        <p>
                            Direction: {{ ict.order_flow.direction|replace('_', ' ')|title }}<br>
                            Strength: {{ ict.order_flow.strength|title }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk Management -->
        {% if recommendation.risk_management %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card analysis-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Risk Management</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Max Risk:</strong><br>
                                {{ recommendation.risk_management.max_risk_percent }}%
                            </div>
                            <div class="col-md-3">
                                <strong>Risk Amount:</strong><br>
                                ${{ recommendation.risk_management.risk_amount_usd }}
                            </div>
                            <div class="col-md-3">
                                <strong>Position Size:</strong><br>
                                {{ recommendation.risk_management.suggested_position_size }}
                            </div>
                            <div class="col-md-3">
                                <strong>Best R:R:</strong><br>
                                1:{{ recommendation.risk_management.risk_reward_ratios.tp1 }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Reasoning -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card analysis-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Analysis Reasoning</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            {% for reason in recommendation.reasoning %}
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>{{ reason }}
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Feedback Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card analysis-card">
                    <div class="card-header bg-dark text-white">
                        <h5 class="mb-0"><i class="fas fa-star me-2"></i>Rate This Analysis</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">
                            Your feedback helps improve our analysis accuracy. Please rate the quality of this recommendation.
                        </p>

                        <form action="/feedback" method="post" id="feedbackForm">
                            <input type="hidden" name="analysis_id" value="{{ analysis_id }}">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-star me-2"></i>Overall Accuracy (1-5 stars)
                                    </label>
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                        <input type="radio" name="accuracy_rating" value="{{ i }}" id="star{{ i }}" required>
                                        <label for="star{{ i }}" class="star-label">
                                            <i class="fas fa-star"></i>
                                        </label>
                                        {% endfor %}
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="trade_outcome" class="form-label">
                                        <i class="fas fa-chart-line me-2"></i>Trade Outcome (if taken)
                                    </label>
                                    <select class="form-select" name="trade_outcome" id="trade_outcome">
                                        <option value="">Select outcome</option>
                                        <option value="profit">Profitable Trade</option>
                                        <option value="loss">Loss Trade</option>
                                        <option value="breakeven">Breakeven</option>
                                        <option value="not_taken">Trade Not Taken</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="entry_accuracy" class="form-label">Entry Accuracy</label>
                                    <select class="form-select" name="entry_accuracy">
                                        <option value="">Rate 1-5</option>
                                        {% for i in range(1, 6) %}
                                        <option value="{{ i }}">{{ i }} Star{{ 's' if i > 1 else '' }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="stop_loss_accuracy" class="form-label">Stop Loss Accuracy</label>
                                    <select class="form-select" name="stop_loss_accuracy">
                                        <option value="">Rate 1-5</option>
                                        {% for i in range(1, 6) %}
                                        <option value="{{ i }}">{{ i }} Star{{ 's' if i > 1 else '' }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="take_profit_accuracy" class="form-label">Take Profit Accuracy</label>
                                    <select class="form-select" name="take_profit_accuracy">
                                        <option value="">Rate 1-5</option>
                                        {% for i in range(1, 6) %}
                                        <option value="{{ i }}">{{ i }} Star{{ 's' if i > 1 else '' }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="timing_accuracy" class="form-label">Timing Accuracy</label>
                                    <select class="form-select" name="timing_accuracy">
                                        <option value="">Rate 1-5</option>
                                        {% for i in range(1, 6) %}
                                        <option value="{{ i }}">{{ i }} Star{{ 's' if i > 1 else '' }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="comments" class="form-label">
                                    <i class="fas fa-comment me-2"></i>Additional Comments
                                </label>
                                <textarea class="form-control" name="comments" id="comments" rows="3"
                                          placeholder="Share your thoughts on this analysis..."></textarea>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Submit Feedback
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Market Context -->
        {% if recommendation.market_context %}
        <div class="row mb-4">
            <div class="col-12">
                <div class="card analysis-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="fas fa-globe me-2"></i>Market Context</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Analysis Quality:</strong><br>
                                <span class="badge
                                    {% if recommendation.market_context.analysis_quality == 'high' %}bg-success
                                    {% elif recommendation.market_context.analysis_quality == 'medium' %}bg-warning text-dark
                                    {% else %}bg-danger{% endif %}">
                                    {{ recommendation.market_context.analysis_quality|title }}
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>Market Phase:</strong><br>
                                {{ recommendation.market_context.market_phase|replace('_', ' ')|title }}
                            </div>
                            <div class="col-md-3">
                                <strong>Session:</strong><br>
                                {{ recommendation.market_context.session_context|replace('_', ' ')|title }}
                            </div>
                            <div class="col-md-3">
                                <strong>Sentiment:</strong><br>
                                <span class="badge
                                    {% if recommendation.market_context.overall_sentiment == 'bullish' %}bg-success
                                    {% elif recommendation.market_context.overall_sentiment == 'bearish' %}bg-danger
                                    {% else %}bg-secondary{% endif %}">
                                    {{ recommendation.market_context.overall_sentiment|title }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="row">
            <div class="col-12 text-center">
                <a href="/" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-plus me-2"></i>New Analysis
                </a>
                <a href="/history" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-history me-2"></i>View History
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // Star rating functionality
        document.querySelectorAll('.rating-stars input[type="radio"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const stars = this.closest('.rating-stars').querySelectorAll('.star-label');
                const rating = parseInt(this.value);

                stars.forEach((star, index) => {
                    if (index < rating) {
                        star.style.color = '#ffc107';
                    } else {
                        star.style.color = '#dee2e6';
                    }
                });
            });
        });

        // Initialize star colors
        document.querySelectorAll('.star-label').forEach(star => {
            star.style.color = '#dee2e6';
            star.style.cursor = 'pointer';
            star.style.fontSize = '1.5rem';
        });

        // Form submission
        document.getElementById('feedbackForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
            submitBtn.disabled = true;
        });
    </script>

    <style>
        .rating-stars {
            display: flex;
            gap: 0.5rem;
        }

        .rating-stars input[type="radio"] {
            display: none;
        }

        .star-label {
            cursor: pointer;
            font-size: 1.5rem;
            color: #dee2e6;
            transition: color 0.2s ease;
        }

        .star-label:hover {
            color: #ffc107;
        }
    </style>
</body>
</html>
