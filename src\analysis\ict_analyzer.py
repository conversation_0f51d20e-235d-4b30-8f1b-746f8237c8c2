"""
Inner Circle Trader (ICT) Analysis Module
Implements ICT methodology for XAUUSD trading analysis
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime, time

class ICTAnalyzer:
    """Inner Circle Trader methodology analyzer"""
    
    def __init__(self):
        # ICT Kill Zones (UTC times)
        self.kill_zones = {
            'london': {'start': time(2, 0), 'end': time(5, 0)},
            'new_york': {'start': time(13, 30), 'end': time(16, 30)},
            'asian': {'start': time(20, 0), 'end': time(23, 59)}
        }
        
        # Premium/Discount levels (Fibonacci)
        self.premium_levels = [0.618, 0.705, 0.79, 0.886]
        self.discount_levels = [0.382, 0.295, 0.21, 0.114]
        self.ote_levels = [0.618, 0.705]  # Optimal Trade Entry
        
        self.institutional_candle_threshold = 0.003  # 0.3%
    
    def analyze(self, chart_data: Dict) -> Dict:
        """Main ICT analysis method"""
        try:
            # Extract relevant data
            price_data = chart_data.get('price_data', {})
            candlestick_data = chart_data.get('candlestick_data', {})
            patterns = chart_data.get('patterns', {})
            chart_info = chart_data.get('chart_info', {})
            
            # Perform ICT analysis
            market_maker_model = self._analyze_market_maker_model(candlestick_data, patterns)
            premium_discount = self._analyze_premium_discount(price_data)
            institutional_candles = self._identify_institutional_candles(candlestick_data)
            kill_zone_analysis = self._analyze_kill_zones(chart_info)
            order_flow = self._analyze_order_flow(candlestick_data, patterns)
            
            # Calculate ICT score
            ict_score = self._calculate_ict_score(
                market_maker_model, premium_discount, institutional_candles,
                kill_zone_analysis, order_flow
            )
            
            return {
                'market_maker_model': market_maker_model,
                'premium_discount': premium_discount,
                'institutional_candles': institutional_candles,
                'kill_zone_analysis': kill_zone_analysis,
                'order_flow': order_flow,
                'ict_score': ict_score,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'error': f"ICT analysis failed: {str(e)}",
                'market_maker_model': {},
                'premium_discount': {},
                'institutional_candles': {},
                'kill_zone_analysis': {},
                'order_flow': {},
                'ict_score': 0.0
            }
    
    def _analyze_market_maker_model(self, candlestick_data: Dict, patterns: Dict) -> Dict:
        """Analyze market using ICT Market Maker Model"""
        
        price_movement = candlestick_data.get('price_movement', 'unclear')
        volatility = candlestick_data.get('estimated_volatility', 0)
        trend_direction = patterns.get('trend_direction', 'unclear')
        consolidation = patterns.get('consolidation', False)
        
        # Determine market maker phase
        mm_phase = self._determine_mm_phase(price_movement, volatility, consolidation)
        
        # Assess manipulation potential
        manipulation = self._assess_manipulation(volatility, price_movement, trend_direction)
        
        # Identify distribution/accumulation
        distribution_accumulation = self._identify_distribution_accumulation(
            candlestick_data, patterns
        )
        
        return {
            'phase': mm_phase,
            'manipulation': manipulation,
            'distribution_accumulation': distribution_accumulation,
            'model_confidence': self._calculate_mm_confidence(mm_phase, manipulation)
        }
    
    def _determine_mm_phase(self, price_movement: str, volatility: float, consolidation: bool) -> str:
        """Determine current Market Maker phase"""
        
        if consolidation and volatility < 30:
            return 'accumulation'
        elif price_movement in ['bullish', 'bearish'] and volatility > 50:
            return 'manipulation'
        elif price_movement in ['bullish', 'bearish'] and volatility > 30:
            return 'distribution'
        else:
            return 'consolidation'
    
    def _assess_manipulation(self, volatility: float, price_movement: str, trend_direction: str) -> Dict:
        """Assess potential market manipulation"""
        
        # Look for signs of manipulation (false moves, stop hunts)
        manipulation_signals = []
        
        if volatility > 60:
            manipulation_signals.append('high_volatility')
        
        if price_movement != 'unclear' and trend_direction == 'sideways':
            manipulation_signals.append('directional_move_in_range')
        
        if price_movement == 'bullish' and trend_direction == 'downtrend':
            manipulation_signals.append('counter_trend_move')
        elif price_movement == 'bearish' and trend_direction == 'uptrend':
            manipulation_signals.append('counter_trend_move')
        
        manipulation_probability = len(manipulation_signals) / 3.0  # Normalize to 0-1
        
        return {
            'probability': min(0.9, manipulation_probability),
            'signals': manipulation_signals,
            'strength': 'high' if manipulation_probability > 0.6 else 'medium' if manipulation_probability > 0.3 else 'low'
        }
    
    def _identify_distribution_accumulation(self, candlestick_data: Dict, patterns: Dict) -> Dict:
        """Identify distribution or accumulation phases"""
        
        bullish_candles = candlestick_data.get('bullish_candles', 0)
        bearish_candles = candlestick_data.get('bearish_candles', 0)
        consolidation = patterns.get('consolidation', False)
        
        total_candles = bullish_candles + bearish_candles
        if total_candles == 0:
            return {'phase': 'unclear', 'strength': 'weak'}
        
        bullish_ratio = bullish_candles / total_candles
        
        if consolidation:
            if bullish_ratio > 0.6:
                return {'phase': 'accumulation', 'strength': 'strong', 'bias': 'bullish'}
            elif bullish_ratio < 0.4:
                return {'phase': 'distribution', 'strength': 'strong', 'bias': 'bearish'}
            else:
                return {'phase': 'neutral', 'strength': 'medium', 'bias': 'neutral'}
        else:
            if bullish_ratio > 0.7:
                return {'phase': 'bullish_distribution', 'strength': 'medium', 'bias': 'bullish'}
            elif bullish_ratio < 0.3:
                return {'phase': 'bearish_distribution', 'strength': 'medium', 'bias': 'bearish'}
            else:
                return {'phase': 'mixed', 'strength': 'weak', 'bias': 'neutral'}
    
    def _calculate_mm_confidence(self, phase: str, manipulation: Dict) -> float:
        """Calculate confidence in Market Maker model analysis"""
        
        base_confidence = 0.5
        
        # Phase confidence
        if phase in ['accumulation', 'distribution', 'manipulation']:
            base_confidence += 0.2
        elif phase == 'consolidation':
            base_confidence += 0.1
        
        # Manipulation confidence
        manip_strength = manipulation.get('strength', 'low')
        if manip_strength == 'high':
            base_confidence += 0.2
        elif manip_strength == 'medium':
            base_confidence += 0.1
        
        return min(0.9, base_confidence)
    
    def _analyze_premium_discount(self, price_data: Dict) -> Dict:
        """Analyze premium/discount levels using ICT methodology"""
        
        detected_prices = price_data.get('detected_prices', [])
        price_range = price_data.get('price_range', {})
        
        if not detected_prices or not price_range.get('high') or not price_range.get('low'):
            return {
                'current_level': 'unknown',
                'premium_levels': [],
                'discount_levels': [],
                'ote_levels': [],
                'recommendation': 'insufficient_data'
            }
        
        high = price_range['high']
        low = price_range['low']
        range_size = high - low
        
        # Calculate current price position (assuming last detected price is current)
        current_price = detected_prices[-1] if detected_prices else (high + low) / 2
        current_position = (current_price - low) / range_size if range_size > 0 else 0.5
        
        # Determine if in premium or discount
        if current_position > 0.5:
            current_level = 'premium'
        else:
            current_level = 'discount'
        
        # Calculate key levels
        premium_prices = [low + (level * range_size) for level in self.premium_levels]
        discount_prices = [low + (level * range_size) for level in self.discount_levels]
        ote_prices = [low + (level * range_size) for level in self.ote_levels]
        
        # Generate recommendation
        recommendation = self._generate_premium_discount_recommendation(
            current_level, current_position
        )
        
        return {
            'current_level': current_level,
            'current_position': round(current_position, 3),
            'premium_levels': [round(p, 2) for p in premium_prices],
            'discount_levels': [round(p, 2) for p in discount_prices],
            'ote_levels': [round(p, 2) for p in ote_prices],
            'recommendation': recommendation
        }
    
    def _generate_premium_discount_recommendation(self, current_level: str, position: float) -> str:
        """Generate trading recommendation based on premium/discount analysis"""
        
        if current_level == 'discount' and position < 0.4:
            return 'look_for_buys'
        elif current_level == 'premium' and position > 0.6:
            return 'look_for_sells'
        elif 0.4 <= position <= 0.6:
            return 'equilibrium_wait'
        else:
            return 'monitor_for_reversal'
    
    def _identify_institutional_candles(self, candlestick_data: Dict) -> Dict:
        """Identify institutional-sized candles"""
        
        volatility = candlestick_data.get('estimated_volatility', 0)
        bullish_candles = candlestick_data.get('bullish_candles', 0)
        bearish_candles = candlestick_data.get('bearish_candles', 0)
        
        # Estimate institutional candles based on volatility
        institutional_threshold = 50  # Volatility threshold for institutional activity
        
        if volatility > institutional_threshold:
            institutional_count = max(1, int(volatility / 30))
            strength = 'strong' if volatility > 80 else 'medium'
        else:
            institutional_count = 0
            strength = 'weak'
        
        # Determine bias
        total_candles = bullish_candles + bearish_candles
        if total_candles > 0:
            bullish_ratio = bullish_candles / total_candles
            if bullish_ratio > 0.6:
                bias = 'bullish'
            elif bullish_ratio < 0.4:
                bias = 'bearish'
            else:
                bias = 'neutral'
        else:
            bias = 'neutral'
        
        return {
            'count': institutional_count,
            'strength': strength,
            'bias': bias,
            'significance': 'high' if institutional_count >= 2 else 'medium' if institutional_count == 1 else 'low'
        }

    def _analyze_kill_zones(self, chart_info: Dict) -> Dict:
        """Analyze ICT Kill Zones timing"""

        # Note: In a real implementation, you would extract actual time from the chart
        # For now, we'll provide general kill zone analysis

        current_time = datetime.now().time()

        # Determine which kill zone we're in (if any)
        active_zone = None
        for zone_name, zone_times in self.kill_zones.items():
            if self._is_time_in_range(current_time, zone_times['start'], zone_times['end']):
                active_zone = zone_name
                break

        # Assess kill zone strength
        zone_strength = self._assess_kill_zone_strength(active_zone)

        return {
            'active_zone': active_zone,
            'zone_strength': zone_strength,
            'london_zone': self._format_kill_zone('london'),
            'new_york_zone': self._format_kill_zone('new_york'),
            'asian_zone': self._format_kill_zone('asian'),
            'recommendation': self._generate_kill_zone_recommendation(active_zone, zone_strength)
        }

    def _is_time_in_range(self, current: time, start: time, end: time) -> bool:
        """Check if current time is within kill zone range"""
        if start <= end:
            return start <= current <= end
        else:  # Crosses midnight
            return current >= start or current <= end

    def _assess_kill_zone_strength(self, active_zone: Optional[str]) -> str:
        """Assess strength of current kill zone"""
        if active_zone == 'london':
            return 'high'  # London session is typically most volatile for XAUUSD
        elif active_zone == 'new_york':
            return 'high'  # NY session also high impact
        elif active_zone == 'asian':
            return 'medium'  # Asian session typically lower volatility
        else:
            return 'low'  # Outside kill zones

    def _format_kill_zone(self, zone_name: str) -> Dict:
        """Format kill zone information"""
        zone = self.kill_zones[zone_name]
        return {
            'name': zone_name.title(),
            'start': zone['start'].strftime('%H:%M'),
            'end': zone['end'].strftime('%H:%M'),
            'timezone': 'UTC'
        }

    def _generate_kill_zone_recommendation(self, active_zone: Optional[str], strength: str) -> str:
        """Generate recommendation based on kill zone analysis"""
        if active_zone and strength in ['high', 'medium']:
            return f'active_{active_zone}_session'
        elif active_zone:
            return 'low_activity_period'
        else:
            return 'outside_kill_zones'

    def _analyze_order_flow(self, candlestick_data: Dict, patterns: Dict) -> Dict:
        """Analyze institutional order flow"""

        price_movement = candlestick_data.get('price_movement', 'unclear')
        volatility = candlestick_data.get('estimated_volatility', 0)
        candle_ratio = candlestick_data.get('candle_ratio', 1)
        trend_direction = patterns.get('trend_direction', 'unclear')

        # Assess order flow strength
        flow_strength = self._assess_order_flow_strength(volatility, candle_ratio)

        # Determine flow direction
        flow_direction = self._determine_order_flow_direction(price_movement, trend_direction)

        # Calculate flow momentum
        momentum = self._calculate_flow_momentum(volatility, candle_ratio, price_movement)

        # Assess institutional participation
        institutional_participation = self._assess_institutional_participation(
            flow_strength, volatility
        )

        return {
            'strength': flow_strength,
            'direction': flow_direction,
            'momentum': momentum,
            'institutional_participation': institutional_participation,
            'flow_confidence': self._calculate_order_flow_confidence(
                flow_strength, flow_direction, momentum
            )
        }

    def _assess_order_flow_strength(self, volatility: float, candle_ratio: float) -> str:
        """Assess strength of order flow"""

        # Strong order flow indicated by high volatility and imbalanced candles
        if volatility > 60 and abs(candle_ratio - 1) > 0.5:
            return 'strong'
        elif volatility > 40 and abs(candle_ratio - 1) > 0.3:
            return 'medium'
        elif volatility > 20:
            return 'weak'
        else:
            return 'very_weak'

    def _determine_order_flow_direction(self, price_movement: str, trend_direction: str) -> str:
        """Determine order flow direction"""

        if price_movement == 'bullish':
            if trend_direction == 'uptrend':
                return 'bullish_continuation'
            elif trend_direction == 'downtrend':
                return 'bullish_reversal'
            else:
                return 'bullish_breakout'
        elif price_movement == 'bearish':
            if trend_direction == 'downtrend':
                return 'bearish_continuation'
            elif trend_direction == 'uptrend':
                return 'bearish_reversal'
            else:
                return 'bearish_breakout'
        else:
            return 'neutral'

    def _calculate_flow_momentum(self, volatility: float, candle_ratio: float,
                               price_movement: str) -> Dict:
        """Calculate order flow momentum"""

        # Momentum based on volatility and directional bias
        momentum_score = volatility / 100.0  # Normalize volatility

        # Adjust for directional bias
        if price_movement in ['bullish', 'bearish']:
            momentum_score *= (1 + abs(candle_ratio - 1))

        momentum_score = min(1.0, momentum_score)

        if momentum_score > 0.7:
            momentum_strength = 'strong'
        elif momentum_score > 0.4:
            momentum_strength = 'medium'
        else:
            momentum_strength = 'weak'

        return {
            'score': round(momentum_score, 3),
            'strength': momentum_strength,
            'direction': price_movement
        }

    def _assess_institutional_participation(self, flow_strength: str, volatility: float) -> Dict:
        """Assess level of institutional participation"""

        # High volatility + strong flow suggests institutional activity
        if flow_strength == 'strong' and volatility > 50:
            participation = 'high'
            confidence = 0.8
        elif flow_strength in ['strong', 'medium'] and volatility > 30:
            participation = 'medium'
            confidence = 0.6
        elif flow_strength in ['medium', 'weak'] and volatility > 15:
            participation = 'low'
            confidence = 0.4
        else:
            participation = 'minimal'
            confidence = 0.2

        return {
            'level': participation,
            'confidence': confidence,
            'indicators': self._get_institutional_indicators(flow_strength, volatility)
        }

    def _get_institutional_indicators(self, flow_strength: str, volatility: float) -> List[str]:
        """Get indicators of institutional participation"""
        indicators = []

        if volatility > 60:
            indicators.append('high_volatility')
        if flow_strength == 'strong':
            indicators.append('strong_directional_flow')
        if volatility > 40 and flow_strength in ['strong', 'medium']:
            indicators.append('coordinated_movement')

        return indicators

    def _calculate_order_flow_confidence(self, strength: str, direction: str,
                                       momentum: Dict) -> float:
        """Calculate confidence in order flow analysis"""

        base_confidence = 0.5

        # Strength contribution
        if strength == 'strong':
            base_confidence += 0.25
        elif strength == 'medium':
            base_confidence += 0.15
        elif strength == 'weak':
            base_confidence += 0.05

        # Direction contribution
        if direction != 'neutral':
            base_confidence += 0.1

        # Momentum contribution
        momentum_strength = momentum.get('strength', 'weak')
        if momentum_strength == 'strong':
            base_confidence += 0.15
        elif momentum_strength == 'medium':
            base_confidence += 0.1

        return min(0.95, base_confidence)

    def _calculate_ict_score(self, market_maker_model: Dict, premium_discount: Dict,
                           institutional_candles: Dict, kill_zone_analysis: Dict,
                           order_flow: Dict) -> float:
        """Calculate overall ICT analysis score"""

        scores = []

        # Market Maker Model score
        mm_confidence = market_maker_model.get('model_confidence', 0.5)
        scores.append(mm_confidence)

        # Premium/Discount score
        if premium_discount.get('current_level') != 'unknown':
            pd_score = 0.8 if premium_discount.get('recommendation') in ['look_for_buys', 'look_for_sells'] else 0.5
        else:
            pd_score = 0.3
        scores.append(pd_score)

        # Institutional candles score
        inst_significance = institutional_candles.get('significance', 'low')
        if inst_significance == 'high':
            scores.append(0.9)
        elif inst_significance == 'medium':
            scores.append(0.6)
        else:
            scores.append(0.3)

        # Kill zone score
        zone_strength = kill_zone_analysis.get('zone_strength', 'low')
        if zone_strength == 'high':
            scores.append(0.8)
        elif zone_strength == 'medium':
            scores.append(0.5)
        else:
            scores.append(0.2)

        # Order flow score
        flow_confidence = order_flow.get('flow_confidence', 0.5)
        scores.append(flow_confidence)

        # Calculate weighted average
        weights = [0.25, 0.2, 0.2, 0.15, 0.2]  # Market maker model gets highest weight
        weighted_score = sum(score * weight for score, weight in zip(scores, weights))

        return round(weighted_score, 3)
