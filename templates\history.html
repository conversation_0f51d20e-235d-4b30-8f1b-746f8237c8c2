<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analysis History - XAUUSD Trading Analysis</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .history-card {
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            transition: transform 0.2s ease;
        }
        .history-card:hover {
            transform: translateY(-2px);
        }
        .action-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
        .action-buy { background: linear-gradient(45deg, #28a745, #20c997); }
        .action-sell { background: linear-gradient(45deg, #dc3545, #e83e8c); }
        .action-wait { background: linear-gradient(45deg, #6c757d, #adb5bd); }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>XAUUSD Analysis Pro
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>New Analysis</a>
                <a class="nav-link active" href="/history"><i class="fas fa-history me-1"></i>History</a>
                <a class="nav-link" href="/performance"><i class="fas fa-chart-bar me-1"></i>Performance</a>
            </div>
        </div>
    </nav>

    <div class="container py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-5 fw-bold">
                    <i class="fas fa-history me-3"></i>Analysis History
                </h1>
                <p class="text-muted">Review your previous XAUUSD trading analysis results</p>
            </div>
        </div>

        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <select class="form-select" id="strategyFilter">
                                    <option value="">All Strategies</option>
                                    <option value="scalping">Scalping</option>
                                    <option value="swing">Swing Trading</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="actionFilter">
                                    <option value="">All Actions</option>
                                    <option value="buy">Buy</option>
                                    <option value="sell">Sell</option>
                                    <option value="wait">Wait</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <input type="date" class="form-control" id="dateFilter">
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-primary" onclick="applyFilters()">
                                    <i class="fas fa-filter me-2"></i>Filter
                                </button>
                                <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                    <i class="fas fa-times me-2"></i>Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analysis History -->
        <div class="row">
            <div class="col-12">
                {% if analyses %}
                    {% for analysis in analyses %}
                    {% set result = analysis.get_result_dict() %}
                    {% set recommendation = result.recommendation %}
                    <div class="card history-card" data-strategy="{{ analysis.strategy }}" 
                         data-action="{{ recommendation.action if recommendation else 'unknown' }}"
                         data-date="{{ analysis.created_at.strftime('%Y-%m-%d') }}">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-2">
                                    <div class="text-center">
                                        <span class="badge action-badge action-{{ recommendation.action if recommendation else 'wait' }} text-white">
                                            {% if recommendation and recommendation.action == 'buy' %}
                                                <i class="fas fa-arrow-up me-1"></i>BUY
                                            {% elif recommendation and recommendation.action == 'sell' %}
                                                <i class="fas fa-arrow-down me-1"></i>SELL
                                            {% else %}
                                                <i class="fas fa-pause me-1"></i>WAIT
                                            {% endif %}
                                        </span>
                                        <div class="small text-muted mt-1">
                                            {{ analysis.strategy|title }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-3">
                                    <strong>{{ analysis.filename }}</strong><br>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ analysis.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                </div>
                                
                                <div class="col-md-2">
                                    {% if recommendation and recommendation.confidence %}
                                    <div class="text-center">
                                        <div class="fw-bold">{{ (recommendation.confidence * 100)|round|int }}%</div>
                                        <small class="text-muted">Confidence</small>
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-3">
                                    {% if recommendation and recommendation.levels %}
                                    <small>
                                        <strong>Entry:</strong> ${{ recommendation.levels.entry }}<br>
                                        <strong>SL:</strong> ${{ recommendation.levels.stop_loss }}<br>
                                        <strong>TP1:</strong> ${{ recommendation.levels.take_profit_1 }}
                                    </small>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-2 text-end">
                                    {% set accuracy = analysis.calculate_accuracy_score() %}
                                    {% if accuracy %}
                                    <div class="mb-2">
                                        <span class="badge 
                                            {% if accuracy >= 0.8 %}bg-success
                                            {% elif accuracy >= 0.6 %}bg-warning text-dark
                                            {% else %}bg-danger{% endif %}">
                                            {{ (accuracy * 100)|round|int }}% Accuracy
                                        </span>
                                    </div>
                                    {% endif %}
                                    
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewDetails({{ analysis.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="downloadReport({{ analysis.id }})">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Feedback Summary -->
                            {% if analysis.feedbacks %}
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="border-top pt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-comments me-1"></i>
                                            {{ analysis.feedbacks|length }} feedback(s) |
                                            {% set outcomes = analysis.get_trade_outcome_summary() %}
                                            {% if outcomes %}
                                                {% for outcome, count in outcomes.items() %}
                                                    {{ outcome|title }}: {{ count }}{% if not loop.last %}, {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">No Analysis History</h4>
                        <p class="text-muted">Start by uploading your first XAUUSD chart for analysis.</p>
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create First Analysis
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Pagination -->
        {% if analyses and analyses|length >= 50 %}
        <div class="row mt-4">
            <div class="col-12">
                <nav>
                    <ul class="pagination justify-content-center">
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="loadMore()">
                                <i class="fas fa-chevron-down me-2"></i>Load More
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        function applyFilters() {
            const strategy = document.getElementById('strategyFilter').value;
            const action = document.getElementById('actionFilter').value;
            const date = document.getElementById('dateFilter').value;
            
            const cards = document.querySelectorAll('.history-card');
            
            cards.forEach(card => {
                let show = true;
                
                if (strategy && card.dataset.strategy !== strategy) show = false;
                if (action && card.dataset.action !== action) show = false;
                if (date && card.dataset.date !== date) show = false;
                
                card.style.display = show ? 'block' : 'none';
            });
        }
        
        function clearFilters() {
            document.getElementById('strategyFilter').value = '';
            document.getElementById('actionFilter').value = '';
            document.getElementById('dateFilter').value = '';
            
            document.querySelectorAll('.history-card').forEach(card => {
                card.style.display = 'block';
            });
        }
        
        function viewDetails(analysisId) {
            // In a real implementation, this would show a modal or navigate to details page
            alert(`View details for analysis ${analysisId}`);
        }
        
        function downloadReport(analysisId) {
            // In a real implementation, this would generate and download a PDF report
            alert(`Download report for analysis ${analysisId}`);
        }
        
        function loadMore() {
            // In a real implementation, this would load more results via AJAX
            alert('Load more functionality would be implemented here');
        }
    </script>
</body>
</html>
