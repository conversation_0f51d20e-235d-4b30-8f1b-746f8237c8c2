"""
Smart Money Concepts (SMC) Analysis Module
Implements SMC methodology for XAUUSD trading analysis
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime

class SMCAnalyzer:
    """Smart Money Concepts analyzer for trading charts"""
    
    def __init__(self):
        self.order_block_threshold = 0.002  # 0.2% price movement
        self.fair_value_gap_min_size = 0.001  # 0.1% minimum gap
        self.liquidity_sweep_threshold = 0.0015  # 0.15% threshold
        self.market_structure_lookback = 50
        
    def analyze(self, chart_data: Dict) -> Dict:
        """Main SMC analysis method"""
        try:
            # Extract relevant data
            price_data = chart_data.get('price_data', {})
            candlestick_data = chart_data.get('candlestick_data', {})
            patterns = chart_data.get('patterns', {})
            sr_levels = chart_data.get('support_resistance', {})
            
            # Perform SMC analysis
            market_structure = self._analyze_market_structure(patterns, sr_levels)
            order_blocks = self._identify_order_blocks(candlestick_data, price_data)
            fair_value_gaps = self._identify_fair_value_gaps(candlestick_data)
            liquidity_analysis = self._analyze_liquidity(price_data, sr_levels)
            institutional_flow = self._analyze_institutional_flow(candlestick_data, patterns)
            
            # Calculate SMC score
            smc_score = self._calculate_smc_score(
                market_structure, order_blocks, fair_value_gaps, 
                liquidity_analysis, institutional_flow
            )
            
            return {
                'market_structure': market_structure,
                'order_blocks': order_blocks,
                'fair_value_gaps': fair_value_gaps,
                'liquidity_analysis': liquidity_analysis,
                'institutional_flow': institutional_flow,
                'smc_score': smc_score,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'error': f"SMC analysis failed: {str(e)}",
                'market_structure': {},
                'order_blocks': {},
                'fair_value_gaps': {},
                'liquidity_analysis': {},
                'institutional_flow': {},
                'smc_score': 0.0
            }
    
    def _analyze_market_structure(self, patterns: Dict, sr_levels: Dict) -> Dict:
        """Analyze market structure using SMC principles"""
        
        trend_direction = patterns.get('trend_direction', 'unclear')
        consolidation = patterns.get('consolidation', False)
        sr_strength = sr_levels.get('sr_strength', 'weak')
        
        # Determine market structure state
        if trend_direction in ['uptrend', 'downtrend'] and sr_strength == 'strong':
            structure_state = 'trending_with_structure'
            structure_strength = 'strong'
        elif trend_direction in ['uptrend', 'downtrend']:
            structure_state = 'trending'
            structure_strength = 'medium'
        elif consolidation:
            structure_state = 'consolidation'
            structure_strength = 'medium'
        else:
            structure_state = 'unclear'
            structure_strength = 'weak'
        
        # Identify key levels
        key_levels = self._identify_key_levels(sr_levels)
        
        # Assess structure breaks
        structure_breaks = self._assess_structure_breaks(patterns, sr_levels)
        
        return {
            'state': structure_state,
            'strength': structure_strength,
            'trend_direction': trend_direction,
            'key_levels': key_levels,
            'structure_breaks': structure_breaks,
            'consolidation_detected': consolidation
        }
    
    def _identify_key_levels(self, sr_levels: Dict) -> Dict:
        """Identify key support and resistance levels"""
        support_count = sr_levels.get('support_levels', 0)
        resistance_count = sr_levels.get('resistance_levels', 0)
        total_levels = sr_levels.get('total_sr_levels', 0)
        
        return {
            'support_levels': support_count,
            'resistance_levels': resistance_count,
            'total_levels': total_levels,
            'level_quality': 'high' if total_levels >= 3 else 'medium' if total_levels >= 2 else 'low'
        }
    
    def _assess_structure_breaks(self, patterns: Dict, sr_levels: Dict) -> Dict:
        """Assess potential structure breaks"""
        breakout_potential = patterns.get('breakout_potential', 'low')
        reversal_signals = patterns.get('reversal_signals', [])
        
        break_probability = 'low'
        if breakout_potential == 'high' and len(reversal_signals) > 0:
            break_probability = 'high'
        elif breakout_potential == 'medium':
            break_probability = 'medium'
        
        return {
            'break_probability': break_probability,
            'breakout_potential': breakout_potential,
            'reversal_signals_count': len(reversal_signals)
        }
    
    def _identify_order_blocks(self, candlestick_data: Dict, price_data: Dict) -> Dict:
        """Identify institutional order blocks"""
        
        # Analyze candlestick patterns for order block characteristics
        bullish_candles = candlestick_data.get('bullish_candles', 0)
        bearish_candles = candlestick_data.get('bearish_candles', 0)
        volatility = candlestick_data.get('estimated_volatility', 0)
        
        # Identify potential order blocks based on price action
        bullish_order_blocks = self._detect_bullish_order_blocks(bullish_candles, volatility)
        bearish_order_blocks = self._detect_bearish_order_blocks(bearish_candles, volatility)
        
        return {
            'bullish_order_blocks': bullish_order_blocks,
            'bearish_order_blocks': bearish_order_blocks,
            'total_order_blocks': bullish_order_blocks['count'] + bearish_order_blocks['count'],
            'dominant_bias': self._determine_order_block_bias(bullish_order_blocks, bearish_order_blocks)
        }
    
    def _detect_bullish_order_blocks(self, bullish_candles: int, volatility: float) -> Dict:
        """Detect bullish order blocks"""
        # Simplified order block detection
        # In real implementation, this would analyze actual OHLC data
        
        if bullish_candles > 5 and volatility > 30:
            strength = 'strong'
            count = 2
        elif bullish_candles > 3:
            strength = 'medium'
            count = 1
        else:
            strength = 'weak'
            count = 0
        
        return {
            'count': count,
            'strength': strength,
            'confidence': min(0.9, (bullish_candles * volatility) / 500)
        }
    
    def _detect_bearish_order_blocks(self, bearish_candles: int, volatility: float) -> Dict:
        """Detect bearish order blocks"""
        # Simplified order block detection
        
        if bearish_candles > 5 and volatility > 30:
            strength = 'strong'
            count = 2
        elif bearish_candles > 3:
            strength = 'medium'
            count = 1
        else:
            strength = 'weak'
            count = 0
        
        return {
            'count': count,
            'strength': strength,
            'confidence': min(0.9, (bearish_candles * volatility) / 500)
        }
    
    def _determine_order_block_bias(self, bullish_obs: Dict, bearish_obs: Dict) -> str:
        """Determine overall order block bias"""
        bullish_score = bullish_obs['count'] * (1 if bullish_obs['strength'] == 'strong' else 0.5)
        bearish_score = bearish_obs['count'] * (1 if bearish_obs['strength'] == 'strong' else 0.5)
        
        if bullish_score > bearish_score * 1.2:
            return 'bullish'
        elif bearish_score > bullish_score * 1.2:
            return 'bearish'
        else:
            return 'neutral'
    
    def _identify_fair_value_gaps(self, candlestick_data: Dict) -> Dict:
        """Identify fair value gaps (imbalances)"""
        
        volatility = candlestick_data.get('estimated_volatility', 0)
        price_movement = candlestick_data.get('price_movement', 'unclear')
        
        # Estimate FVG presence based on volatility and movement
        if volatility > 50 and price_movement in ['bullish', 'bearish']:
            fvg_count = 2
            fvg_strength = 'strong'
        elif volatility > 30:
            fvg_count = 1
            fvg_strength = 'medium'
        else:
            fvg_count = 0
            fvg_strength = 'weak'
        
        return {
            'count': fvg_count,
            'strength': fvg_strength,
            'direction': price_movement,
            'fill_probability': self._calculate_fvg_fill_probability(fvg_strength, price_movement)
        }
    
    def _calculate_fvg_fill_probability(self, strength: str, direction: str) -> float:
        """Calculate probability of FVG being filled"""
        base_probability = 0.7  # FVGs have high probability of being filled
        
        if strength == 'strong':
            return min(0.9, base_probability + 0.2)
        elif strength == 'medium':
            return base_probability
        else:
            return max(0.3, base_probability - 0.3)
    
    def _analyze_liquidity(self, price_data: Dict, sr_levels: Dict) -> Dict:
        """Analyze liquidity pools and sweeps"""
        
        detected_prices = price_data.get('detected_prices', [])
        price_range = price_data.get('price_range', {})
        total_sr_levels = sr_levels.get('total_sr_levels', 0)
        
        # Estimate liquidity based on price levels and S/R
        liquidity_pools = self._identify_liquidity_pools(detected_prices, total_sr_levels)
        sweep_potential = self._assess_sweep_potential(price_range, total_sr_levels)
        
        return {
            'liquidity_pools': liquidity_pools,
            'sweep_potential': sweep_potential,
            'liquidity_strength': self._calculate_liquidity_strength(liquidity_pools, sweep_potential)
        }
    
    def _identify_liquidity_pools(self, prices: List, sr_levels: int) -> Dict:
        """Identify potential liquidity pools"""
        if not prices:
            return {'count': 0, 'strength': 'weak'}
        
        # Liquidity pools typically form around key levels
        pool_count = min(sr_levels, len(prices) // 3)
        
        if pool_count >= 3:
            strength = 'strong'
        elif pool_count >= 2:
            strength = 'medium'
        else:
            strength = 'weak'
        
        return {
            'count': pool_count,
            'strength': strength
        }
    
    def _assess_sweep_potential(self, price_range: Dict, sr_levels: int) -> Dict:
        """Assess potential for liquidity sweeps"""
        range_size = price_range.get('range', 0)
        
        if range_size and sr_levels >= 2:
            if range_size > 20:  # Large range suggests potential for sweeps
                potential = 'high'
            elif range_size > 10:
                potential = 'medium'
            else:
                potential = 'low'
        else:
            potential = 'low'
        
        return {
            'potential': potential,
            'range_size': range_size
        }
    
    def _calculate_liquidity_strength(self, pools: Dict, sweep_potential: Dict) -> str:
        """Calculate overall liquidity strength"""
        pool_strength = pools.get('strength', 'weak')
        sweep_pot = sweep_potential.get('potential', 'low')
        
        if pool_strength == 'strong' and sweep_pot in ['high', 'medium']:
            return 'strong'
        elif pool_strength in ['strong', 'medium'] or sweep_pot in ['high', 'medium']:
            return 'medium'
        else:
            return 'weak'

    def _analyze_institutional_flow(self, candlestick_data: Dict, patterns: Dict) -> Dict:
        """Analyze institutional order flow"""

        price_movement = candlestick_data.get('price_movement', 'unclear')
        volatility = candlestick_data.get('estimated_volatility', 0)
        candle_ratio = candlestick_data.get('candle_ratio', 1)
        trend_direction = patterns.get('trend_direction', 'unclear')

        # Assess institutional participation
        institutional_strength = self._assess_institutional_strength(volatility, candle_ratio)
        flow_direction = self._determine_flow_direction(price_movement, trend_direction)
        smart_money_bias = self._calculate_smart_money_bias(institutional_strength, flow_direction)

        return {
            'institutional_strength': institutional_strength,
            'flow_direction': flow_direction,
            'smart_money_bias': smart_money_bias,
            'confidence': self._calculate_flow_confidence(institutional_strength, flow_direction)
        }

    def _assess_institutional_strength(self, volatility: float, candle_ratio: float) -> str:
        """Assess strength of institutional participation"""

        # High volatility + imbalanced candle ratio suggests institutional activity
        if volatility > 50 and abs(candle_ratio - 1) > 0.4:
            return 'strong'
        elif volatility > 30 and abs(candle_ratio - 1) > 0.2:
            return 'medium'
        else:
            return 'weak'

    def _determine_flow_direction(self, price_movement: str, trend_direction: str) -> str:
        """Determine institutional flow direction"""

        if price_movement == 'bullish' and trend_direction == 'uptrend':
            return 'bullish_continuation'
        elif price_movement == 'bearish' and trend_direction == 'downtrend':
            return 'bearish_continuation'
        elif price_movement == 'bullish' and trend_direction == 'downtrend':
            return 'bullish_reversal'
        elif price_movement == 'bearish' and trend_direction == 'uptrend':
            return 'bearish_reversal'
        else:
            return 'neutral'

    def _calculate_smart_money_bias(self, institutional_strength: str, flow_direction: str) -> str:
        """Calculate overall smart money bias"""

        if institutional_strength == 'strong':
            if 'bullish' in flow_direction:
                return 'bullish'
            elif 'bearish' in flow_direction:
                return 'bearish'

        if institutional_strength == 'medium':
            if flow_direction in ['bullish_continuation', 'bullish_reversal']:
                return 'slightly_bullish'
            elif flow_direction in ['bearish_continuation', 'bearish_reversal']:
                return 'slightly_bearish'

        return 'neutral'

    def _calculate_flow_confidence(self, institutional_strength: str, flow_direction: str) -> float:
        """Calculate confidence in flow analysis"""

        base_confidence = 0.5

        if institutional_strength == 'strong':
            base_confidence += 0.3
        elif institutional_strength == 'medium':
            base_confidence += 0.1

        if 'continuation' in flow_direction:
            base_confidence += 0.2
        elif 'reversal' in flow_direction:
            base_confidence += 0.1

        return min(0.95, base_confidence)

    def _calculate_smc_score(self, market_structure: Dict, order_blocks: Dict,
                           fair_value_gaps: Dict, liquidity_analysis: Dict,
                           institutional_flow: Dict) -> float:
        """Calculate overall SMC analysis score"""

        scores = []

        # Market structure score
        structure_strength = market_structure.get('strength', 'weak')
        if structure_strength == 'strong':
            scores.append(0.9)
        elif structure_strength == 'medium':
            scores.append(0.6)
        else:
            scores.append(0.3)

        # Order blocks score
        ob_total = order_blocks.get('total_order_blocks', 0)
        ob_score = min(0.9, ob_total * 0.3)
        scores.append(ob_score)

        # Fair value gaps score
        fvg_count = fair_value_gaps.get('count', 0)
        fvg_score = min(0.8, fvg_count * 0.4)
        scores.append(fvg_score)

        # Liquidity score
        liquidity_strength = liquidity_analysis.get('liquidity_strength', 'weak')
        if liquidity_strength == 'strong':
            scores.append(0.8)
        elif liquidity_strength == 'medium':
            scores.append(0.5)
        else:
            scores.append(0.2)

        # Institutional flow score
        flow_confidence = institutional_flow.get('confidence', 0.5)
        scores.append(flow_confidence)

        # Calculate weighted average
        weights = [0.25, 0.2, 0.2, 0.2, 0.15]  # Market structure gets highest weight
        weighted_score = sum(score * weight for score, weight in zip(scores, weights))

        return round(weighted_score, 3)
