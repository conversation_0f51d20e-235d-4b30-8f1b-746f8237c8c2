"""
XAUUSD Trading Analysis Application
Main Flask application entry point
"""

import os
import sys
from flask import Flask, request, render_template, jsonify, redirect, url_for
from werkzeug.utils import secure_filename
from datetime import datetime
import json
import traceback

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config import Config
from src.database import db, init_db
from src.analysis.chart_processor import ChartProcessor
from src.analysis.smc_analyzer import SMCAnalyzer
from src.analysis.ict_analyzer import ICTAnalyzer
from src.analysis.strategy_engine import StrategyEngine
from src.learning.feedback_system import FeedbackSystem
from src.models.analysis import Analysis
from src.models.feedback import Feedback

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # Initialize database
    db.init_app(app)

    # Create upload directory
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # Initialize analysis components
    chart_processor = ChartProcessor()
    smc_analyzer = SMCAnalyzer()
    ict_analyzer = ICTAnalyzer()
    strategy_engine = StrategyEngine()
    feedback_system = FeedbackSystem()

    # Load existing learning state
    feedback_system.load_learning_state()

    @app.route('/')
    def index():
        return render_template('index.html')

    @app.route('/upload', methods=['POST'])
    def upload_chart():
        try:
            if 'chart' not in request.files:
                return jsonify({'error': 'No chart file uploaded'}), 400

            file = request.files['chart']
            strategy = request.form.get('strategy', 'swing')

            if file.filename == '':
                return jsonify({'error': 'No file selected'}), 400

            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                filename = timestamp + filename
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(filepath)

                # Process the chart
                analysis_result = process_chart_analysis(
                    filepath, strategy, chart_processor, smc_analyzer,
                    ict_analyzer, strategy_engine
                )

                # Save analysis to database
                analysis = Analysis(
                    filename=filename,
                    strategy=strategy,
                    result=json.dumps(analysis_result),
                    timestamp=datetime.now()
                )
                db.session.add(analysis)
                db.session.commit()

                return render_template('result.html',
                                     analysis=analysis_result,
                                     analysis_id=analysis.id)

            return jsonify({'error': 'Invalid file type'}), 400

        except Exception as e:
            print(f"Error in upload_chart: {str(e)}")
            print(traceback.format_exc())
            return jsonify({'error': f'Analysis failed: {str(e)}'}), 500

    @app.route('/feedback', methods=['POST'])
    def submit_feedback():
        try:
            analysis_id = request.form.get('analysis_id')
            accuracy_rating = int(request.form.get('accuracy_rating'))
            trade_outcome = request.form.get('trade_outcome')
            comments = request.form.get('comments', '')

            # Save feedback
            feedback = Feedback(
                analysis_id=analysis_id,
                accuracy_rating=accuracy_rating,
                trade_outcome=trade_outcome,
                comments=comments,
                timestamp=datetime.now()
            )
            db.session.add(feedback)
            db.session.commit()

            # Update learning system
            feedback_result = feedback_system.process_feedback(feedback)

            return redirect(url_for('index'))

        except Exception as e:
            print(f"Error in submit_feedback: {str(e)}")
            print(traceback.format_exc())
            return jsonify({'error': f'Feedback submission failed: {str(e)}'}), 500

    @app.route('/history')
    def analysis_history():
        try:
            analyses = Analysis.query.order_by(Analysis.timestamp.desc()).limit(50).all()
            return render_template('history.html', analyses=analyses)
        except Exception as e:
            print(f"Error in analysis_history: {str(e)}")
            return jsonify({'error': f'Failed to load history: {str(e)}'}), 500

    @app.route('/performance')
    def performance_dashboard():
        try:
            performance_summary = feedback_system.get_performance_summary()
            recommendations = feedback_system.get_recommendations_for_improvement()

            return render_template('performance.html',
                                 performance=performance_summary,
                                 recommendations=recommendations)
        except Exception as e:
            print(f"Error in performance_dashboard: {str(e)}")
            return jsonify({'error': f'Failed to load performance data: {str(e)}'}), 500

    def allowed_file(filename):
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

    def process_chart_analysis(filepath, strategy, chart_processor, smc_analyzer, ict_analyzer, strategy_engine):
        """Main analysis pipeline"""

        try:
            # Step 1: Extract chart data from image
            chart_data = chart_processor.extract_chart_data(filepath)

            # Step 2: SMC Analysis
            smc_analysis = smc_analyzer.analyze(chart_data)

            # Step 3: ICT Analysis
            ict_analysis = ict_analyzer.analyze(chart_data)

            # Step 4: Generate trading strategy
            trade_recommendation = strategy_engine.generate_recommendation(
                chart_data, smc_analysis, ict_analysis, strategy
            )

            return {
                'chart_data': chart_data,
                'smc_analysis': smc_analysis,
                'ict_analysis': ict_analysis,
                'recommendation': trade_recommendation,
                'strategy': strategy,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"Error in process_chart_analysis: {str(e)}")
            print(traceback.format_exc())
            raise

    return app

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        init_db()
    app.run(debug=True, host='0.0.0.0', port=5000)
