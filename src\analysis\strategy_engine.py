"""
Trading Strategy Engine for XAUUSD Analysis
Combines SMC and ICT analysis to generate trading recommendations
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime

class StrategyEngine:
    """Main strategy engine combining SMC and ICT methodologies"""
    
    def __init__(self):
        # Risk management parameters
        self.max_risk_per_trade = 0.02  # 2%
        self.min_risk_reward_ratio = 1.5
        self.scalping_max_risk = 0.01  # 1%
        self.swing_max_risk = 0.03  # 3%
        
        # XAUUSD specific parameters
        self.pip_value = 0.01
        self.typical_spread = 0.3  # 30 cents
        self.average_daily_range = 25.0  # $25 typical daily range
        
    def generate_recommendation(self, chart_data: Dict, smc_analysis: Dict, 
                              ict_analysis: Dict, strategy: str) -> Dict:
        """Generate comprehensive trading recommendation"""
        
        try:
            # Combine analysis results
            combined_analysis = self._combine_analyses(smc_analysis, ict_analysis)
            
            # Generate strategy-specific recommendation
            if strategy == 'scalping':
                recommendation = self._generate_scalping_recommendation(
                    chart_data, combined_analysis
                )
            else:  # swing trading
                recommendation = self._generate_swing_recommendation(
                    chart_data, combined_analysis
                )
            
            # Add risk management
            recommendation = self._apply_risk_management(recommendation, strategy)
            
            # Calculate confidence score
            recommendation['confidence'] = self._calculate_recommendation_confidence(
                combined_analysis, recommendation
            )
            
            # Add market context
            recommendation['market_context'] = self._generate_market_context(
                smc_analysis, ict_analysis
            )
            
            return recommendation
            
        except Exception as e:
            return {
                'error': f"Strategy generation failed: {str(e)}",
                'action': 'no_trade',
                'confidence': 0.0,
                'reasoning': ['Analysis failed - avoid trading']
            }
    
    def _combine_analyses(self, smc_analysis: Dict, ict_analysis: Dict) -> Dict:
        """Combine SMC and ICT analysis results"""
        
        # Extract key signals from both analyses
        smc_score = smc_analysis.get('smc_score', 0.0)
        ict_score = ict_analysis.get('ict_score', 0.0)
        
        # Market structure from SMC
        market_structure = smc_analysis.get('market_structure', {})
        structure_state = market_structure.get('state', 'unclear')
        trend_direction = market_structure.get('trend_direction', 'unclear')
        
        # Order flow from ICT
        order_flow = ict_analysis.get('order_flow', {})
        flow_direction = order_flow.get('direction', 'neutral')
        flow_strength = order_flow.get('strength', 'weak')
        
        # Premium/Discount from ICT
        premium_discount = ict_analysis.get('premium_discount', {})
        current_level = premium_discount.get('current_level', 'unknown')
        pd_recommendation = premium_discount.get('recommendation', 'insufficient_data')
        
        # Smart money bias from SMC
        institutional_flow = smc_analysis.get('institutional_flow', {})
        smart_money_bias = institutional_flow.get('smart_money_bias', 'neutral')
        
        # Combine scores
        combined_score = (smc_score + ict_score) / 2.0
        
        # Determine overall bias
        overall_bias = self._determine_overall_bias(
            trend_direction, flow_direction, smart_money_bias, pd_recommendation
        )
        
        return {
            'combined_score': combined_score,
            'overall_bias': overall_bias,
            'structure_state': structure_state,
            'trend_direction': trend_direction,
            'flow_direction': flow_direction,
            'flow_strength': flow_strength,
            'current_level': current_level,
            'pd_recommendation': pd_recommendation,
            'smart_money_bias': smart_money_bias,
            'smc_score': smc_score,
            'ict_score': ict_score
        }
    
    def _determine_overall_bias(self, trend_direction: str, flow_direction: str, 
                              smart_money_bias: str, pd_recommendation: str) -> str:
        """Determine overall market bias from multiple signals"""
        
        bullish_signals = 0
        bearish_signals = 0
        
        # Trend direction
        if trend_direction == 'uptrend':
            bullish_signals += 1
        elif trend_direction == 'downtrend':
            bearish_signals += 1
        
        # Flow direction
        if 'bullish' in flow_direction:
            bullish_signals += 1
        elif 'bearish' in flow_direction:
            bearish_signals += 1
        
        # Smart money bias
        if 'bullish' in smart_money_bias:
            bullish_signals += 1
        elif 'bearish' in smart_money_bias:
            bearish_signals += 1
        
        # Premium/Discount recommendation
        if pd_recommendation == 'look_for_buys':
            bullish_signals += 1
        elif pd_recommendation == 'look_for_sells':
            bearish_signals += 1
        
        # Determine overall bias
        if bullish_signals > bearish_signals + 1:
            return 'bullish'
        elif bearish_signals > bullish_signals + 1:
            return 'bearish'
        else:
            return 'neutral'
    
    def _generate_scalping_recommendation(self, chart_data: Dict, 
                                        combined_analysis: Dict) -> Dict:
        """Generate scalping-specific recommendation"""
        
        overall_bias = combined_analysis.get('overall_bias', 'neutral')
        flow_strength = combined_analysis.get('flow_strength', 'weak')
        combined_score = combined_analysis.get('combined_score', 0.0)
        current_level = combined_analysis.get('current_level', 'unknown')
        
        # Scalping requires strong momentum and clear direction
        if flow_strength in ['strong', 'medium'] and combined_score > 0.6:
            if overall_bias == 'bullish':
                action = 'buy'
                entry_logic = 'Strong bullish momentum detected'
            elif overall_bias == 'bearish':
                action = 'sell'
                entry_logic = 'Strong bearish momentum detected'
            else:
                action = 'wait'
                entry_logic = 'Mixed signals - wait for clarity'
        else:
            action = 'wait'
            entry_logic = 'Insufficient momentum for scalping'
        
        # Generate entry and exit levels for scalping
        if action in ['buy', 'sell']:
            levels = self._calculate_scalping_levels(chart_data, action, current_level)
        else:
            levels = {}
        
        return {
            'action': action,
            'strategy': 'scalping',
            'entry_logic': entry_logic,
            'timeframe_focus': ['M1', 'M5', 'M15'],
            'levels': levels,
            'reasoning': self._generate_scalping_reasoning(
                overall_bias, flow_strength, combined_score
            )
        }
    
    def _generate_swing_recommendation(self, chart_data: Dict, 
                                     combined_analysis: Dict) -> Dict:
        """Generate swing trading recommendation"""
        
        overall_bias = combined_analysis.get('overall_bias', 'neutral')
        structure_state = combined_analysis.get('structure_state', 'unclear')
        trend_direction = combined_analysis.get('trend_direction', 'unclear')
        combined_score = combined_analysis.get('combined_score', 0.0)
        pd_recommendation = combined_analysis.get('pd_recommendation', 'insufficient_data')
        
        # Swing trading focuses on structure and longer-term bias
        if structure_state in ['trending_with_structure', 'trending'] and combined_score > 0.5:
            if overall_bias == 'bullish' and pd_recommendation in ['look_for_buys', 'equilibrium_wait']:
                action = 'buy'
                entry_logic = 'Bullish structure with favorable entry conditions'
            elif overall_bias == 'bearish' and pd_recommendation in ['look_for_sells', 'equilibrium_wait']:
                action = 'sell'
                entry_logic = 'Bearish structure with favorable entry conditions'
            else:
                action = 'wait'
                entry_logic = 'Structure present but entry conditions not optimal'
        elif structure_state == 'consolidation':
            action = 'wait'
            entry_logic = 'Market in consolidation - wait for breakout'
        else:
            action = 'wait'
            entry_logic = 'Unclear market structure - avoid trading'
        
        # Generate entry and exit levels for swing trading
        if action in ['buy', 'sell']:
            levels = self._calculate_swing_levels(chart_data, action, trend_direction)
        else:
            levels = {}
        
        return {
            'action': action,
            'strategy': 'swing',
            'entry_logic': entry_logic,
            'timeframe_focus': ['H1', 'H4', 'D1'],
            'levels': levels,
            'reasoning': self._generate_swing_reasoning(
                overall_bias, structure_state, trend_direction, pd_recommendation
            )
        }
    
    def _calculate_scalping_levels(self, chart_data: Dict, action: str, 
                                 current_level: str) -> Dict:
        """Calculate entry/exit levels for scalping"""
        
        price_data = chart_data.get('price_data', {})
        price_range = price_data.get('price_range', {})
        
        if not price_range.get('high') or not price_range.get('low'):
            return {}
        
        high = price_range['high']
        low = price_range['low']
        range_size = high - low
        
        # Estimate current price (middle of range for simplicity)
        current_price = (high + low) / 2
        
        if action == 'buy':
            # Scalping buy levels
            entry = current_price + (range_size * 0.02)  # Slight premium entry
            stop_loss = entry - (range_size * 0.05)  # Tight stop for scalping
            take_profit_1 = entry + (range_size * 0.03)  # Quick profit target
            take_profit_2 = entry + (range_size * 0.05)
            take_profit_3 = entry + (range_size * 0.08)
        else:  # sell
            # Scalping sell levels
            entry = current_price - (range_size * 0.02)  # Slight discount entry
            stop_loss = entry + (range_size * 0.05)  # Tight stop for scalping
            take_profit_1 = entry - (range_size * 0.03)  # Quick profit target
            take_profit_2 = entry - (range_size * 0.05)
            take_profit_3 = entry - (range_size * 0.08)
        
        return {
            'entry': round(entry, 2),
            'stop_loss': round(stop_loss, 2),
            'take_profit_1': round(take_profit_1, 2),
            'take_profit_2': round(take_profit_2, 2),
            'take_profit_3': round(take_profit_3, 2),
            'risk_reward_1': round(abs(take_profit_1 - entry) / abs(entry - stop_loss), 2),
            'risk_reward_2': round(abs(take_profit_2 - entry) / abs(entry - stop_loss), 2),
            'risk_reward_3': round(abs(take_profit_3 - entry) / abs(entry - stop_loss), 2)
        }

    def _calculate_swing_levels(self, chart_data: Dict, action: str,
                              trend_direction: str) -> Dict:
        """Calculate entry/exit levels for swing trading"""

        price_data = chart_data.get('price_data', {})
        price_range = price_data.get('price_range', {})

        if not price_range.get('high') or not price_range.get('low'):
            return {}

        high = price_range['high']
        low = price_range['low']
        range_size = high - low

        # Estimate current price
        current_price = (high + low) / 2

        if action == 'buy':
            # Swing buy levels - wider stops and targets
            entry = current_price + (range_size * 0.05)  # Wait for pullback
            stop_loss = entry - (range_size * 0.15)  # Wider stop for swing
            take_profit_1 = entry + (range_size * 0.20)  # Conservative target
            take_profit_2 = entry + (range_size * 0.35)  # Medium target
            take_profit_3 = entry + (range_size * 0.50)  # Aggressive target
        else:  # sell
            # Swing sell levels
            entry = current_price - (range_size * 0.05)  # Wait for pullback
            stop_loss = entry + (range_size * 0.15)  # Wider stop for swing
            take_profit_1 = entry - (range_size * 0.20)  # Conservative target
            take_profit_2 = entry - (range_size * 0.35)  # Medium target
            take_profit_3 = entry - (range_size * 0.50)  # Aggressive target

        return {
            'entry': round(entry, 2),
            'stop_loss': round(stop_loss, 2),
            'take_profit_1': round(take_profit_1, 2),
            'take_profit_2': round(take_profit_2, 2),
            'take_profit_3': round(take_profit_3, 2),
            'risk_reward_1': round(abs(take_profit_1 - entry) / abs(entry - stop_loss), 2),
            'risk_reward_2': round(abs(take_profit_2 - entry) / abs(entry - stop_loss), 2),
            'risk_reward_3': round(abs(take_profit_3 - entry) / abs(entry - stop_loss), 2)
        }

    def _generate_scalping_reasoning(self, overall_bias: str, flow_strength: str,
                                   combined_score: float) -> List[str]:
        """Generate reasoning for scalping recommendation"""

        reasoning = []

        reasoning.append(f"Overall market bias: {overall_bias}")
        reasoning.append(f"Order flow strength: {flow_strength}")
        reasoning.append(f"Combined analysis score: {combined_score:.2f}")

        if flow_strength == 'strong':
            reasoning.append("Strong momentum favors scalping opportunities")
        elif flow_strength == 'medium':
            reasoning.append("Moderate momentum - proceed with caution")
        else:
            reasoning.append("Weak momentum - scalping not recommended")

        if combined_score > 0.7:
            reasoning.append("High confidence setup - favorable for entry")
        elif combined_score > 0.5:
            reasoning.append("Medium confidence - wait for better confirmation")
        else:
            reasoning.append("Low confidence - avoid trading")

        reasoning.append("Scalping requires tight risk management and quick execution")

        return reasoning

    def _generate_swing_reasoning(self, overall_bias: str, structure_state: str,
                                trend_direction: str, pd_recommendation: str) -> List[str]:
        """Generate reasoning for swing trading recommendation"""

        reasoning = []

        reasoning.append(f"Market structure: {structure_state}")
        reasoning.append(f"Trend direction: {trend_direction}")
        reasoning.append(f"Overall bias: {overall_bias}")
        reasoning.append(f"Premium/Discount analysis: {pd_recommendation}")

        if structure_state == 'trending_with_structure':
            reasoning.append("Strong trending market with clear structure")
        elif structure_state == 'trending':
            reasoning.append("Trending market - look for pullback entries")
        elif structure_state == 'consolidation':
            reasoning.append("Market consolidating - wait for breakout")
        else:
            reasoning.append("Unclear market structure - avoid trading")

        if pd_recommendation == 'look_for_buys':
            reasoning.append("Price in discount zone - favorable for long positions")
        elif pd_recommendation == 'look_for_sells':
            reasoning.append("Price in premium zone - favorable for short positions")
        elif pd_recommendation == 'equilibrium_wait':
            reasoning.append("Price at equilibrium - wait for clear direction")

        reasoning.append("Swing trading allows for wider stops and larger profit targets")

        return reasoning

    def _apply_risk_management(self, recommendation: Dict, strategy: str) -> Dict:
        """Apply risk management rules to recommendation"""

        levels = recommendation.get('levels', {})
        if not levels:
            return recommendation

        entry = levels.get('entry')
        stop_loss = levels.get('stop_loss')

        if not entry or not stop_loss:
            return recommendation

        # Calculate risk per trade
        risk_amount = abs(entry - stop_loss)

        # Apply strategy-specific risk limits
        max_risk = self.scalping_max_risk if strategy == 'scalping' else self.swing_max_risk

        # Calculate position size (simplified)
        account_balance = 10000  # Assume $10,000 account for calculation
        max_risk_amount = account_balance * max_risk
        position_size = max_risk_amount / risk_amount if risk_amount > 0 else 0

        # Add risk management info
        recommendation['risk_management'] = {
            'max_risk_percent': max_risk * 100,
            'risk_amount_usd': round(risk_amount, 2),
            'suggested_position_size': round(position_size, 2),
            'risk_reward_ratios': {
                'tp1': levels.get('risk_reward_1', 0),
                'tp2': levels.get('risk_reward_2', 0),
                'tp3': levels.get('risk_reward_3', 0)
            }
        }

        # Validate minimum risk-reward ratio
        min_rr = levels.get('risk_reward_1', 0)
        if min_rr < self.min_risk_reward_ratio:
            recommendation['action'] = 'wait'
            recommendation['risk_warning'] = f"Risk-reward ratio {min_rr:.2f} below minimum {self.min_risk_reward_ratio}"

        return recommendation

    def _calculate_recommendation_confidence(self, combined_analysis: Dict,
                                          recommendation: Dict) -> float:
        """Calculate overall confidence in recommendation"""

        base_confidence = combined_analysis.get('combined_score', 0.5)

        # Adjust based on action
        action = recommendation.get('action', 'wait')
        if action == 'wait':
            return max(0.3, base_confidence * 0.7)  # Lower confidence for wait signals

        # Adjust based on risk-reward
        levels = recommendation.get('levels', {})
        if levels:
            min_rr = levels.get('risk_reward_1', 0)
            if min_rr >= 2.0:
                base_confidence += 0.1
            elif min_rr < 1.5:
                base_confidence -= 0.2

        # Adjust based on strategy alignment
        strategy = recommendation.get('strategy', 'swing')
        flow_strength = combined_analysis.get('flow_strength', 'weak')

        if strategy == 'scalping' and flow_strength in ['strong', 'medium']:
            base_confidence += 0.1
        elif strategy == 'swing' and combined_analysis.get('structure_state') in ['trending_with_structure', 'trending']:
            base_confidence += 0.1

        return min(0.95, max(0.1, base_confidence))

    def _generate_market_context(self, smc_analysis: Dict, ict_analysis: Dict) -> Dict:
        """Generate market context summary"""

        # Extract key information
        smc_score = smc_analysis.get('smc_score', 0.0)
        ict_score = ict_analysis.get('ict_score', 0.0)

        market_structure = smc_analysis.get('market_structure', {})
        kill_zone = ict_analysis.get('kill_zone_analysis', {})
        premium_discount = ict_analysis.get('premium_discount', {})

        # Generate context
        context = {
            'analysis_quality': 'high' if min(smc_score, ict_score) > 0.6 else 'medium' if min(smc_score, ict_score) > 0.4 else 'low',
            'market_phase': market_structure.get('state', 'unclear'),
            'session_context': kill_zone.get('active_zone', 'outside_kill_zones'),
            'price_level_context': premium_discount.get('current_level', 'unknown'),
            'overall_sentiment': self._determine_market_sentiment(smc_analysis, ict_analysis),
            'key_levels': self._extract_key_levels(smc_analysis, ict_analysis),
            'trading_session_strength': kill_zone.get('zone_strength', 'low')
        }

        return context

    def _determine_market_sentiment(self, smc_analysis: Dict, ict_analysis: Dict) -> str:
        """Determine overall market sentiment"""

        # SMC sentiment indicators
        institutional_flow = smc_analysis.get('institutional_flow', {})
        smart_money_bias = institutional_flow.get('smart_money_bias', 'neutral')

        # ICT sentiment indicators
        order_flow = ict_analysis.get('order_flow', {})
        flow_direction = order_flow.get('direction', 'neutral')

        market_maker = ict_analysis.get('market_maker_model', {})
        mm_phase = market_maker.get('phase', 'unclear')

        # Combine sentiment signals
        bullish_signals = 0
        bearish_signals = 0

        if 'bullish' in smart_money_bias:
            bullish_signals += 1
        elif 'bearish' in smart_money_bias:
            bearish_signals += 1

        if 'bullish' in flow_direction:
            bullish_signals += 1
        elif 'bearish' in flow_direction:
            bearish_signals += 1

        if mm_phase == 'accumulation':
            bullish_signals += 1
        elif mm_phase == 'distribution':
            bearish_signals += 1

        # Determine sentiment
        if bullish_signals > bearish_signals:
            return 'bullish'
        elif bearish_signals > bullish_signals:
            return 'bearish'
        else:
            return 'neutral'

    def _extract_key_levels(self, smc_analysis: Dict, ict_analysis: Dict) -> Dict:
        """Extract key price levels from analysis"""

        # SMC levels
        market_structure = smc_analysis.get('market_structure', {})
        key_levels = market_structure.get('key_levels', {})

        # ICT levels
        premium_discount = ict_analysis.get('premium_discount', {})

        return {
            'support_levels': key_levels.get('support_levels', 0),
            'resistance_levels': key_levels.get('resistance_levels', 0),
            'premium_levels': len(premium_discount.get('premium_levels', [])),
            'discount_levels': len(premium_discount.get('discount_levels', [])),
            'ote_levels': len(premium_discount.get('ote_levels', []))
        }
