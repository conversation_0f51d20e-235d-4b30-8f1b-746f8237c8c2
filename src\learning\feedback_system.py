"""
Feedback Learning System for XAUUSD Trading Analysis
Implements custom machine learning without external AI dependencies
"""

import numpy as np
import json
import pickle
import os
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
from collections import defaultdict

from src.models.feedback import Feedback
from src.models.analysis import Analysis

class FeedbackSystem:
    """Custom learning system that adapts based on user feedback"""
    
    def __init__(self):
        self.learning_rate = 0.01
        self.feedback_weight_decay = 0.95
        self.min_samples_for_learning = 10
        self.confidence_threshold = 0.7
        
        # Learning weights for different analysis components
        self.component_weights = {
            'smc_score': 1.0,
            'ict_score': 1.0,
            'market_structure': 1.0,
            'order_flow': 1.0,
            'premium_discount': 1.0,
            'kill_zones': 0.8,
            'volatility': 0.9
        }
        
        # Performance tracking
        self.performance_history = []
        self.adaptation_history = []
        
    def process_feedback(self, feedback: Feedback) -> Dict:
        """Process user feedback and update learning system"""
        
        try:
            # Get the associated analysis
            analysis = Analysis.query.get(feedback.analysis_id)
            if not analysis:
                return {'error': 'Analysis not found'}
            
            # Extract features from analysis
            features = self._extract_features_from_analysis(analysis)
            
            # Calculate feedback score
            feedback_score = self._calculate_feedback_score(feedback)
            
            # Update learning weights
            weight_updates = self._calculate_weight_updates(features, feedback_score)
            self._apply_weight_updates(weight_updates)
            
            # Track performance
            performance_metrics = self._calculate_performance_metrics(feedback, analysis)
            self.performance_history.append(performance_metrics)
            
            # Adapt thresholds if needed
            adaptations = self._adapt_thresholds(feedback, analysis)
            if adaptations:
                self.adaptation_history.append(adaptations)
            
            # Save learning state
            self._save_learning_state()
            
            return {
                'feedback_processed': True,
                'feedback_score': feedback_score,
                'weight_updates': weight_updates,
                'performance_metrics': performance_metrics,
                'adaptations': adaptations
            }
            
        except Exception as e:
            return {'error': f'Feedback processing failed: {str(e)}'}
    
    def _extract_features_from_analysis(self, analysis: Analysis) -> Dict:
        """Extract features from analysis for learning"""
        
        result_dict = analysis.get_result_dict()
        
        # Extract SMC features
        smc_analysis = result_dict.get('smc_analysis', {})
        smc_score = smc_analysis.get('smc_score', 0.0)
        market_structure = smc_analysis.get('market_structure', {})
        
        # Extract ICT features
        ict_analysis = result_dict.get('ict_analysis', {})
        ict_score = ict_analysis.get('ict_score', 0.0)
        order_flow = ict_analysis.get('order_flow', {})
        premium_discount = ict_analysis.get('premium_discount', {})
        kill_zone = ict_analysis.get('kill_zone_analysis', {})
        
        # Extract chart features
        chart_data = result_dict.get('chart_data', {})
        candlestick_data = chart_data.get('candlestick_data', {})
        
        # Extract recommendation features
        recommendation = result_dict.get('recommendation', {})
        
        return {
            'smc_score': smc_score,
            'ict_score': ict_score,
            'combined_score': (smc_score + ict_score) / 2.0,
            'market_structure_strength': market_structure.get('strength', 'weak'),
            'trend_direction': market_structure.get('trend_direction', 'unclear'),
            'order_flow_strength': order_flow.get('strength', 'weak'),
            'flow_direction': order_flow.get('direction', 'neutral'),
            'premium_discount_level': premium_discount.get('current_level', 'unknown'),
            'kill_zone_strength': kill_zone.get('zone_strength', 'low'),
            'volatility': candlestick_data.get('estimated_volatility', 0),
            'price_movement': candlestick_data.get('price_movement', 'unclear'),
            'strategy': analysis.strategy,
            'confidence': recommendation.get('confidence', 0.5),
            'action': recommendation.get('action', 'wait'),
            'timeframe': analysis.timeframe or 'unknown'
        }
    
    def _calculate_feedback_score(self, feedback: Feedback) -> float:
        """Calculate normalized feedback score (0-1)"""
        
        # Base score from accuracy rating
        base_score = feedback.accuracy_rating / 5.0
        
        # Adjust based on trade outcome
        if feedback.trade_outcome == 'profit':
            base_score += 0.2
        elif feedback.trade_outcome == 'loss':
            base_score -= 0.2
        elif feedback.trade_outcome == 'breakeven':
            base_score += 0.05
        
        # Adjust based on detailed feedback if available
        detailed_scores = []
        if feedback.entry_accuracy:
            detailed_scores.append(feedback.entry_accuracy / 5.0)
        if feedback.stop_loss_accuracy:
            detailed_scores.append(feedback.stop_loss_accuracy / 5.0)
        if feedback.take_profit_accuracy:
            detailed_scores.append(feedback.take_profit_accuracy / 5.0)
        if feedback.timing_accuracy:
            detailed_scores.append(feedback.timing_accuracy / 5.0)
        
        if detailed_scores:
            detailed_avg = sum(detailed_scores) / len(detailed_scores)
            base_score = (base_score + detailed_avg) / 2.0
        
        # Normalize to 0-1 range
        return max(0.0, min(1.0, base_score))
    
    def _calculate_weight_updates(self, features: Dict, feedback_score: float) -> Dict:
        """Calculate updates to component weights based on feedback"""
        
        weight_updates = {}
        
        # Calculate error (difference between expected and actual performance)
        predicted_performance = self._predict_performance(features)
        error = feedback_score - predicted_performance
        
        # Update weights based on error and feature values
        for component, current_weight in self.component_weights.items():
            feature_value = self._get_feature_value_for_component(features, component)
            
            # Calculate weight update using gradient descent-like approach
            update = self.learning_rate * error * feature_value
            
            # Apply decay to prevent overfitting
            update *= self.feedback_weight_decay
            
            weight_updates[component] = update
        
        return weight_updates
    
    def _get_feature_value_for_component(self, features: Dict, component: str) -> float:
        """Get normalized feature value for a specific component"""
        
        if component == 'smc_score':
            return features.get('smc_score', 0.0)
        elif component == 'ict_score':
            return features.get('ict_score', 0.0)
        elif component == 'market_structure':
            strength = features.get('market_structure_strength', 'weak')
            return {'strong': 1.0, 'medium': 0.6, 'weak': 0.3}.get(strength, 0.3)
        elif component == 'order_flow':
            strength = features.get('order_flow_strength', 'weak')
            return {'strong': 1.0, 'medium': 0.6, 'weak': 0.3, 'very_weak': 0.1}.get(strength, 0.3)
        elif component == 'premium_discount':
            level = features.get('premium_discount_level', 'unknown')
            return {'premium': 0.8, 'discount': 0.8, 'unknown': 0.2}.get(level, 0.2)
        elif component == 'kill_zones':
            strength = features.get('kill_zone_strength', 'low')
            return {'high': 1.0, 'medium': 0.6, 'low': 0.3}.get(strength, 0.3)
        elif component == 'volatility':
            volatility = features.get('volatility', 0)
            return min(1.0, volatility / 100.0)  # Normalize volatility
        else:
            return 0.5  # Default value
    
    def _predict_performance(self, features: Dict) -> float:
        """Predict expected performance based on current weights and features"""
        
        weighted_score = 0.0
        total_weight = 0.0
        
        for component, weight in self.component_weights.items():
            feature_value = self._get_feature_value_for_component(features, component)
            weighted_score += weight * feature_value
            total_weight += weight
        
        return weighted_score / total_weight if total_weight > 0 else 0.5
    
    def _apply_weight_updates(self, weight_updates: Dict) -> None:
        """Apply calculated weight updates to component weights"""
        
        for component, update in weight_updates.items():
            if component in self.component_weights:
                # Apply update with bounds checking
                new_weight = self.component_weights[component] + update
                self.component_weights[component] = max(0.1, min(2.0, new_weight))
    
    def _calculate_performance_metrics(self, feedback: Feedback, analysis: Analysis) -> Dict:
        """Calculate performance metrics for tracking"""
        
        return {
            'timestamp': datetime.now().isoformat(),
            'analysis_id': analysis.id,
            'strategy': analysis.strategy,
            'accuracy_rating': feedback.accuracy_rating,
            'trade_outcome': feedback.trade_outcome,
            'feedback_score': self._calculate_feedback_score(feedback),
            'confidence_score': analysis.confidence_score,
            'profit_loss_pips': feedback.profit_loss_pips,
            'trade_duration_hours': feedback.trade_duration_hours
        }
    
    def _adapt_thresholds(self, feedback: Feedback, analysis: Analysis) -> Dict:
        """Adapt system thresholds based on feedback patterns"""
        
        adaptations = {}
        
        # Check if we have enough feedback samples
        if len(self.performance_history) < self.min_samples_for_learning:
            return adaptations
        
        # Analyze recent performance
        recent_performance = self.performance_history[-20:]  # Last 20 feedbacks
        
        # Calculate success rate
        successful_trades = sum(1 for p in recent_performance 
                              if p.get('trade_outcome') == 'profit' or p.get('accuracy_rating', 0) >= 4)
        success_rate = successful_trades / len(recent_performance)
        
        # Adapt confidence threshold
        if success_rate > 0.7 and self.confidence_threshold > 0.5:
            # Lower threshold if performing well
            new_threshold = max(0.5, self.confidence_threshold - 0.05)
            if new_threshold != self.confidence_threshold:
                adaptations['confidence_threshold'] = {
                    'old': self.confidence_threshold,
                    'new': new_threshold,
                    'reason': 'High success rate - lowering threshold'
                }
                self.confidence_threshold = new_threshold
        
        elif success_rate < 0.4 and self.confidence_threshold < 0.9:
            # Raise threshold if performing poorly
            new_threshold = min(0.9, self.confidence_threshold + 0.05)
            if new_threshold != self.confidence_threshold:
                adaptations['confidence_threshold'] = {
                    'old': self.confidence_threshold,
                    'new': new_threshold,
                    'reason': 'Low success rate - raising threshold'
                }
                self.confidence_threshold = new_threshold
        
        # Adapt learning rate based on feedback consistency
        feedback_variance = np.var([p.get('feedback_score', 0.5) for p in recent_performance])
        
        if feedback_variance < 0.1 and self.learning_rate < 0.05:
            # Increase learning rate if feedback is consistent
            new_lr = min(0.05, self.learning_rate * 1.1)
            if abs(new_lr - self.learning_rate) > 0.001:
                adaptations['learning_rate'] = {
                    'old': self.learning_rate,
                    'new': new_lr,
                    'reason': 'Consistent feedback - increasing learning rate'
                }
                self.learning_rate = new_lr
        
        elif feedback_variance > 0.3 and self.learning_rate > 0.005:
            # Decrease learning rate if feedback is inconsistent
            new_lr = max(0.005, self.learning_rate * 0.9)
            if abs(new_lr - self.learning_rate) > 0.001:
                adaptations['learning_rate'] = {
                    'old': self.learning_rate,
                    'new': new_lr,
                    'reason': 'Inconsistent feedback - decreasing learning rate'
                }
                self.learning_rate = new_lr
        
        return adaptations

    def _save_learning_state(self) -> None:
        """Save current learning state to disk"""

        learning_state = {
            'component_weights': self.component_weights,
            'learning_rate': self.learning_rate,
            'confidence_threshold': self.confidence_threshold,
            'performance_history': self.performance_history[-100:],  # Keep last 100
            'adaptation_history': self.adaptation_history[-50:],  # Keep last 50
            'last_updated': datetime.now().isoformat()
        }

        try:
            os.makedirs('data/learning', exist_ok=True)
            with open('data/learning/feedback_system_state.json', 'w') as f:
                json.dump(learning_state, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save learning state: {e}")

    def load_learning_state(self) -> bool:
        """Load learning state from disk"""

        try:
            with open('data/learning/feedback_system_state.json', 'r') as f:
                learning_state = json.load(f)

            self.component_weights = learning_state.get('component_weights', self.component_weights)
            self.learning_rate = learning_state.get('learning_rate', self.learning_rate)
            self.confidence_threshold = learning_state.get('confidence_threshold', self.confidence_threshold)
            self.performance_history = learning_state.get('performance_history', [])
            self.adaptation_history = learning_state.get('adaptation_history', [])

            return True

        except FileNotFoundError:
            print("No existing learning state found - starting fresh")
            return False
        except Exception as e:
            print(f"Warning: Could not load learning state: {e}")
            return False

    def get_performance_summary(self) -> Dict:
        """Get summary of system performance"""

        if not self.performance_history:
            return {
                'total_feedback': 0,
                'average_accuracy': 0.0,
                'success_rate': 0.0,
                'recent_trend': 'insufficient_data'
            }

        total_feedback = len(self.performance_history)

        # Calculate average accuracy
        accuracy_ratings = [p.get('accuracy_rating', 0) for p in self.performance_history]
        average_accuracy = sum(accuracy_ratings) / len(accuracy_ratings) if accuracy_ratings else 0

        # Calculate success rate (profitable trades or high accuracy)
        successful = sum(1 for p in self.performance_history
                        if p.get('trade_outcome') == 'profit' or p.get('accuracy_rating', 0) >= 4)
        success_rate = successful / total_feedback

        # Analyze recent trend
        recent_trend = self._analyze_performance_trend()

        # Strategy-specific performance
        strategy_performance = self._calculate_strategy_performance()

        return {
            'total_feedback': total_feedback,
            'average_accuracy': round(average_accuracy, 2),
            'success_rate': round(success_rate, 3),
            'recent_trend': recent_trend,
            'strategy_performance': strategy_performance,
            'current_weights': self.component_weights,
            'current_thresholds': {
                'confidence': self.confidence_threshold,
                'learning_rate': self.learning_rate
            }
        }

    def _analyze_performance_trend(self) -> str:
        """Analyze recent performance trend"""

        if len(self.performance_history) < 10:
            return 'insufficient_data'

        # Compare recent vs older performance
        recent_performance = self.performance_history[-10:]
        older_performance = self.performance_history[-20:-10] if len(self.performance_history) >= 20 else []

        if not older_performance:
            return 'insufficient_data'

        recent_avg = np.mean([p.get('feedback_score', 0.5) for p in recent_performance])
        older_avg = np.mean([p.get('feedback_score', 0.5) for p in older_performance])

        difference = recent_avg - older_avg

        if difference > 0.1:
            return 'improving'
        elif difference < -0.1:
            return 'declining'
        else:
            return 'stable'

    def _calculate_strategy_performance(self) -> Dict:
        """Calculate performance by strategy type"""

        strategy_stats = defaultdict(list)

        for performance in self.performance_history:
            strategy = performance.get('strategy', 'unknown')
            feedback_score = performance.get('feedback_score', 0.5)
            strategy_stats[strategy].append(feedback_score)

        strategy_performance = {}
        for strategy, scores in strategy_stats.items():
            strategy_performance[strategy] = {
                'count': len(scores),
                'average_score': round(np.mean(scores), 3),
                'success_rate': round(sum(1 for s in scores if s >= 0.6) / len(scores), 3)
            }

        return strategy_performance

    def get_recommendations_for_improvement(self) -> List[str]:
        """Get recommendations for improving system performance"""

        recommendations = []

        if not self.performance_history:
            recommendations.append("Collect more user feedback to improve analysis accuracy")
            return recommendations

        performance_summary = self.get_performance_summary()
        success_rate = performance_summary['success_rate']
        recent_trend = performance_summary['recent_trend']

        # Success rate recommendations
        if success_rate < 0.4:
            recommendations.append("Low success rate detected - consider reviewing analysis methodology")
            recommendations.append("Increase confidence threshold to be more selective with trade recommendations")
        elif success_rate > 0.8:
            recommendations.append("High success rate - consider lowering confidence threshold to capture more opportunities")

        # Trend recommendations
        if recent_trend == 'declining':
            recommendations.append("Performance declining - review recent market conditions and adapt parameters")
            recommendations.append("Consider increasing learning rate to adapt faster to changing conditions")
        elif recent_trend == 'improving':
            recommendations.append("Performance improving - current learning parameters are working well")

        # Strategy-specific recommendations
        strategy_performance = performance_summary['strategy_performance']

        for strategy, stats in strategy_performance.items():
            if stats['success_rate'] < 0.3:
                recommendations.append(f"Poor performance in {strategy} strategy - review methodology")
            elif stats['count'] < 5:
                recommendations.append(f"Limited data for {strategy} strategy - collect more feedback")

        # Component weight recommendations
        extreme_weights = {k: v for k, v in self.component_weights.items() if v > 1.5 or v < 0.3}
        if extreme_weights:
            recommendations.append("Some analysis components have extreme weights - monitor for overfitting")

        return recommendations

    def reset_learning_system(self) -> Dict:
        """Reset learning system to default state"""

        # Backup current state
        backup_state = {
            'component_weights': self.component_weights.copy(),
            'learning_rate': self.learning_rate,
            'confidence_threshold': self.confidence_threshold,
            'performance_history_count': len(self.performance_history),
            'reset_timestamp': datetime.now().isoformat()
        }

        # Reset to defaults
        self.component_weights = {
            'smc_score': 1.0,
            'ict_score': 1.0,
            'market_structure': 1.0,
            'order_flow': 1.0,
            'premium_discount': 1.0,
            'kill_zones': 0.8,
            'volatility': 0.9
        }

        self.learning_rate = 0.01
        self.confidence_threshold = 0.7
        self.performance_history = []
        self.adaptation_history = []

        # Save reset state
        self._save_learning_state()

        return {
            'reset_completed': True,
            'backup_state': backup_state,
            'message': 'Learning system reset to default state'
        }
