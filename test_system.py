#!/usr/bin/env python3
"""
Test script for XAUUSD Trading Analysis System
"""

import sys
import os
import traceback

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all modules can be imported"""
    print("Testing imports...")
    
    try:
        from src.config import Config
        print("✓ Config imported successfully")
        
        from src.database import db, init_db
        print("✓ Database module imported successfully")
        
        from src.analysis.chart_processor import ChartProcessor
        print("✓ ChartProcessor imported successfully")
        
        from src.analysis.smc_analyzer import SMCAnalyzer
        print("✓ SMCAnalyzer imported successfully")
        
        from src.analysis.ict_analyzer import ICTAnalyzer
        print("✓ ICTAnalyzer imported successfully")
        
        from src.analysis.strategy_engine import StrategyEngine
        print("✓ StrategyEngine imported successfully")
        
        from src.learning.feedback_system import FeedbackSystem
        print("✓ FeedbackSystem imported successfully")
        
        from src.models.analysis import Analysis
        from src.models.feedback import Feedback
        print("✓ Database models imported successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {str(e)}")
        print(traceback.format_exc())
        return False

def test_analysis_components():
    """Test analysis components"""
    print("\nTesting analysis components...")
    
    try:
        from src.analysis.chart_processor import ChartProcessor
        from src.analysis.smc_analyzer import SMCAnalyzer
        from src.analysis.ict_analyzer import ICTAnalyzer
        from src.analysis.strategy_engine import StrategyEngine
        
        # Initialize components
        chart_processor = ChartProcessor()
        smc_analyzer = SMCAnalyzer()
        ict_analyzer = ICTAnalyzer()
        strategy_engine = StrategyEngine()
        
        print("✓ All analysis components initialized successfully")
        
        # Test with dummy data
        dummy_chart_data = {
            'chart_info': {'timeframe': 'H1', 'platform': 'MT4', 'symbol_confirmed': True},
            'candlestick_data': {'bullish_candles': 5, 'bearish_candles': 3, 'estimated_volatility': 45.0, 'price_movement': 'bullish'},
            'price_data': {'detected_prices': [1950.0, 1955.0, 1960.0], 'price_range': {'high': 1960.0, 'low': 1950.0, 'range': 10.0}},
            'patterns': {'trend_direction': 'uptrend', 'consolidation': False, 'breakout_potential': 'medium'},
            'support_resistance': {'support_levels': 2, 'resistance_levels': 1, 'total_sr_levels': 3, 'sr_strength': 'medium'},
            'trend_lines': {'trend_lines_count': 2, 'upward_lines': 1, 'downward_lines': 1}
        }
        
        # Test SMC analysis
        smc_result = smc_analyzer.analyze(dummy_chart_data)
        print(f"✓ SMC Analysis completed - Score: {smc_result.get('smc_score', 0):.3f}")
        
        # Test ICT analysis
        ict_result = ict_analyzer.analyze(dummy_chart_data)
        print(f"✓ ICT Analysis completed - Score: {ict_result.get('ict_score', 0):.3f}")
        
        # Test strategy engine
        recommendation = strategy_engine.generate_recommendation(
            dummy_chart_data, smc_result, ict_result, 'swing'
        )
        print(f"✓ Strategy recommendation generated - Action: {recommendation.get('action', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Analysis component test failed: {str(e)}")
        print(traceback.format_exc())
        return False

def test_feedback_system():
    """Test feedback system"""
    print("\nTesting feedback system...")
    
    try:
        from src.learning.feedback_system import FeedbackSystem
        
        feedback_system = FeedbackSystem()
        print("✓ FeedbackSystem initialized successfully")
        
        # Test performance summary
        performance = feedback_system.get_performance_summary()
        print(f"✓ Performance summary generated - Total feedback: {performance['total_feedback']}")
        
        # Test recommendations
        recommendations = feedback_system.get_recommendations_for_improvement()
        print(f"✓ Improvement recommendations generated - Count: {len(recommendations)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Feedback system test failed: {str(e)}")
        print(traceback.format_exc())
        return False

def test_flask_app():
    """Test Flask application creation"""
    print("\nTesting Flask application...")
    
    try:
        from app import create_app
        
        app = create_app()
        print("✓ Flask application created successfully")
        
        # Test configuration
        print(f"✓ Upload folder: {app.config.get('UPLOAD_FOLDER', 'Not set')}")
        print(f"✓ Allowed extensions: {app.config.get('ALLOWED_EXTENSIONS', 'Not set')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Flask application test failed: {str(e)}")
        print(traceback.format_exc())
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("XAUUSD Trading Analysis System - Test Suite")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_analysis_components,
        test_feedback_system,
        test_flask_app
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
        print("\nTo start the application, run:")
        print("python app.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
