<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XAUUSD Trading Analysis - Smart Money Concepts & ICT</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
        }
        .feature-card {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: border-color 0.3s ease;
        }
        .upload-area:hover {
            border-color: #667eea;
        }
        .methodology-badge {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin: 0.2rem;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line me-2"></i>XAUUSD Analysis Pro
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/history"><i class="fas fa-history me-1"></i>History</a>
                <a class="nav-link" href="/performance"><i class="fas fa-chart-bar me-1"></i>Performance</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-3">
                <i class="fas fa-coins me-3"></i>XAUUSD Trading Analysis
            </h1>
            <p class="lead mb-4">
                Professional trading analysis using Smart Money Concepts (SMC) and Inner Circle Trader (ICT) methodologies
            </p>
            <div class="d-flex justify-content-center flex-wrap">
                <span class="methodology-badge">Smart Money Concepts</span>
                <span class="methodology-badge">ICT Methodology</span>
                <span class="methodology-badge">Machine Learning</span>
                <span class="methodology-badge">Risk Management</span>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Features Section -->
        <div class="row mb-5">
            <div class="col-md-4 mb-3">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-brain fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Smart Money Analysis</h5>
                        <p class="card-text">Advanced SMC analysis including order blocks, fair value gaps, and market structure identification.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-3x text-success mb-3"></i>
                        <h5 class="card-title">ICT Kill Zones</h5>
                        <p class="card-text">Institutional order flow analysis with premium/discount levels and optimal trade entry zones.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-robot fa-3x text-warning mb-3"></i>
                        <h5 class="card-title">Adaptive Learning</h5>
                        <p class="card-text">Custom machine learning that improves analysis accuracy based on your trading feedback.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-upload me-2"></i>Upload Chart for Analysis</h4>
                    </div>
                    <div class="card-body">
                        <form action="/upload" method="post" enctype="multipart/form-data" id="analysisForm">
                            <div class="upload-area mb-4">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>Drop your XAUUSD chart here or click to browse</h5>
                                <p class="text-muted">Supports: PNG, JPG, JPEG, GIF, BMP, TIFF, WEBP</p>
                                <input class="form-control" type="file" name="chart" id="chartFile" required
                                       accept=".png,.jpg,.jpeg,.gif,.bmp,.tiff,.webp" style="display: none;">
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('chartFile').click()">
                                    <i class="fas fa-folder-open me-2"></i>Choose File
                                </button>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="strategy" class="form-label">
                                        <i class="fas fa-chess me-2"></i>Trading Strategy
                                    </label>
                                    <select class="form-select" name="strategy" id="strategy" required>
                                        <option value="">Select Strategy</option>
                                        <option value="scalping">Scalping (M1-M15)</option>
                                        <option value="swing">Swing Trading (H1-D1)</option>
                                    </select>
                                    <div class="form-text">
                                        <small>
                                            <strong>Scalping:</strong> Quick trades, tight stops, high frequency<br>
                                            <strong>Swing:</strong> Longer holds, wider stops, trend following
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-info-circle me-2"></i>Analysis Features
                                    </label>
                                    <div class="border rounded p-3 bg-light">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" checked disabled>
                                            <label class="form-check-label">Market Structure Analysis</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" checked disabled>
                                            <label class="form-check-label">Order Block Identification</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" checked disabled>
                                            <label class="form-check-label">Premium/Discount Levels</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" checked disabled>
                                            <label class="form-check-label">Risk Management</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid">
                                <button class="btn btn-primary btn-lg" type="submit" id="analyzeBtn">
                                    <i class="fas fa-chart-line me-2"></i>Analyze Chart
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="alert alert-info">
                    <h5><i class="fas fa-lightbulb me-2"></i>How It Works</h5>
                    <ol class="mb-0">
                        <li><strong>Upload:</strong> Upload a screenshot of your XAUUSD chart from any platform (MT4, MT5, TradingView, etc.)</li>
                        <li><strong>Select Strategy:</strong> Choose between scalping or swing trading approach</li>
                        <li><strong>Analysis:</strong> Our system analyzes using SMC and ICT methodologies</li>
                        <li><strong>Results:</strong> Get detailed trade recommendations with entry, stop loss, and take profit levels</li>
                        <li><strong>Feedback:</strong> Rate the analysis to help improve future recommendations</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // File upload handling
        document.getElementById('chartFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                document.querySelector('.upload-area h5').textContent = `Selected: ${fileName}`;
                document.querySelector('.upload-area i').className = 'fas fa-check-circle fa-3x text-success mb-3';
            }
        });

        // Form submission handling
        document.getElementById('analysisForm').addEventListener('submit', function(e) {
            const btn = document.getElementById('analyzeBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Analyzing...';
            btn.disabled = true;
        });

        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');

        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#667eea';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#dee2e6';
            uploadArea.style.backgroundColor = 'white';
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.borderColor = '#dee2e6';
            uploadArea.style.backgroundColor = 'white';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('chartFile').files = files;
                document.querySelector('.upload-area h5').textContent = `Selected: ${files[0].name}`;
                document.querySelector('.upload-area i').className = 'fas fa-check-circle fa-3x text-success mb-3';
            }
        });
    </script>
</body>
</html>
